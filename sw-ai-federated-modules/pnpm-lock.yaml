lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      react:
        specifier: ^16.14.0
        version: 16.14.0
      react-dom:
        specifier: ^16.14.0
        version: 16.14.0(react@16.14.0)
    devDependencies:
      '@rsbuild/core':
        specifier: ^1.3.22
        version: 1.3.22
      '@rsbuild/plugin-react':
        specifier: ^1.3.2
        version: 1.3.2(@rsbuild/core@1.3.22)
      '@types/react':
        specifier: ^18.3.23
        version: 18.3.23
      '@types/react-dom':
        specifier: ^18.3.7
        version: 18.3.7(@types/react@18.3.23)
      typescript:
        specifier: ^5.8.3
        version: 5.8.3

packages:

  '@module-federation/error-codes@0.14.0':
    resolution: {integrity: sha512-GGk+EoeSACJikZZyShnLshtq9E2eCrDWbRiB4QAFXCX4oYmGgFfzXlx59vMNwqTKPJWxkEGnPYacJMcr2YYjag==}

  '@module-federation/runtime-core@0.14.0':
    resolution: {integrity: sha512-fGE1Ro55zIFDp/CxQuRhKQ1pJvG7P0qvRm2N+4i8z++2bgDjcxnCKUqDJ8lLD+JfJQvUJf0tuSsJPgevzueD4g==}

  '@module-federation/runtime-tools@0.14.0':
    resolution: {integrity: sha512-y/YN0c2DKsLETE+4EEbmYWjqF9G6ZwgZoDIPkaQ9p0pQu0V4YxzWfQagFFxR0RigYGuhJKmSU/rtNoHq+qF8jg==}

  '@module-federation/runtime@0.14.0':
    resolution: {integrity: sha512-kR3cyHw/Y64SEa7mh4CHXOEQYY32LKLK75kJOmBroLNLO7/W01hMNAvGBYTedS7hWpVuefPk1aFZioy3q2VLdQ==}

  '@module-federation/sdk@0.14.0':
    resolution: {integrity: sha512-lg/OWRsh18hsyTCamOOhEX546vbDiA2O4OggTxxH2wTGr156N6DdELGQlYIKfRdU/0StgtQS81Goc0BgDZlx9A==}

  '@module-federation/webpack-bundler-runtime@0.14.0':
    resolution: {integrity: sha512-POWS6cKBicAAQ3DNY5X7XEUSfOfUsRaBNxbuwEfSGlrkTE9UcWheO06QP2ndHi8tHQuUKcIHi2navhPkJ+k5xg==}

  '@rsbuild/core@1.3.22':
    resolution: {integrity: sha512-FGB7m8Tn/uiOhvqk0lw+NRMyD+VYJ+eBqVfpn0X11spkJDiPWn8UkMRvfzCX4XFcNZwRKYuuKJaZK1DNU8UG+w==}
    engines: {node: '>=16.10.0'}
    hasBin: true

  '@rsbuild/plugin-react@1.3.2':
    resolution: {integrity: sha512-H4blXmgvVOrQlVy4ZfJ5IGfQIF5uKwtkGzwVnEsn1HN7DRRI9VlFrcuXj6+e3GigvYxg6TDHAAUJi6FoIGbnKQ==}
    peerDependencies:
      '@rsbuild/core': 1.x

  '@rspack/binding-darwin-arm64@1.3.12':
    resolution: {integrity: sha512-8hKjVTBeWPqkMzFPNWIh72oU9O3vFy3e88wRjMPImDCXBiEYrKqGTTLd/J0SO+efdL3SBD1rX1IvdJpxCv6Yrw==}
    cpu: [arm64]
    os: [darwin]

  '@rspack/binding-darwin-x64@1.3.12':
    resolution: {integrity: sha512-Sj4m+mCUxL7oCpdu7OmWT7fpBM7hywk5CM9RDc3D7StaBZbvNtNftafCrTZzTYKuZrKmemTh5SFzT5Tz7tf6GA==}
    cpu: [x64]
    os: [darwin]

  '@rspack/binding-linux-arm64-gnu@1.3.12':
    resolution: {integrity: sha512-7MuOxf3/Mhv4mgFdLTvgnt/J+VouNR65DEhorth+RZm3LEWojgoFEphSAMAvpvAOpYSS68Sw4SqsOZi719ia2w==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rspack/binding-linux-arm64-musl@1.3.12':
    resolution: {integrity: sha512-s6KKj20T9Z1bA8caIjU6EzJbwyDo1URNFgBAlafCT2UC6yX7flstDJJ38CxZacA9A2P24RuQK2/jPSZpWrTUFA==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rspack/binding-linux-x64-gnu@1.3.12':
    resolution: {integrity: sha512-0w/sRREYbRgHgWvs2uMEJSLfvzbZkPHUg6CMcYQGNVK6axYRot6jPyKetyFYA9pR5fB5rsXegpnFaZaVrRIK2g==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rspack/binding-linux-x64-musl@1.3.12':
    resolution: {integrity: sha512-jEdxkPymkRxbijDRsBGdhopcbGXiXDg59lXqIRkVklqbDmZ/O6DHm7gImmlx5q9FoWbz0gqJuOKBz4JqWxjWVA==}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rspack/binding-win32-arm64-msvc@1.3.12':
    resolution: {integrity: sha512-ZRvUCb3TDLClAqcTsl/o9UdJf0B5CgzAxgdbnYJbldyuyMeTUB4jp20OfG55M3C2Nute2SNhu2bOOp9Se5Ongw==}
    cpu: [arm64]
    os: [win32]

  '@rspack/binding-win32-ia32-msvc@1.3.12':
    resolution: {integrity: sha512-1TKPjuXStPJr14f3ZHuv40Xc/87jUXx10pzVtrPnw+f3hckECHrbYU/fvbVzZyuXbsXtkXpYca6ygCDRJAoNeQ==}
    cpu: [ia32]
    os: [win32]

  '@rspack/binding-win32-x64-msvc@1.3.12':
    resolution: {integrity: sha512-lCR0JfnYKpV+a6r2A2FdxyUKUS4tajePgpPJN5uXDgMGwrDtRqvx+d0BHhwjFudQVJq9VVbRaL89s2MQ6u+xYw==}
    cpu: [x64]
    os: [win32]

  '@rspack/binding@1.3.12':
    resolution: {integrity: sha512-4Ic8lV0+LCBfTlH5aIOujIRWZOtgmG223zC4L3o8WY/+ESAgpdnK6lSSMfcYgRanYLAy3HOmFIp20jwskMpbAg==}

  '@rspack/core@1.3.12':
    resolution: {integrity: sha512-mAPmV4LPPRgxpouUrGmAE4kpF1NEWJGyM5coebsjK/zaCMSjw3mkdxiU2b5cO44oIi0Ifv5iGkvwbdrZOvMyFA==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@swc/helpers': '>=0.5.1'
    peerDependenciesMeta:
      '@swc/helpers':
        optional: true

  '@rspack/lite-tapable@1.0.1':
    resolution: {integrity: sha512-VynGOEsVw2s8TAlLf/uESfrgfrq2+rcXB1muPJYBWbsm1Oa6r5qVQhjA5ggM6z/coYPrsVMgovl3Ff7Q7OCp1w==}
    engines: {node: '>=16.0.0'}

  '@rspack/plugin-react-refresh@1.4.3':
    resolution: {integrity: sha512-wZx4vWgy5oMEvgyNGd/oUKcdnKaccYWHCRkOqTdAPJC3WcytxhTX+Kady8ERurSBiLyQpoMiU3Iyd+F1Y2Arbw==}
    peerDependencies:
      react-refresh: '>=0.10.0 <1.0.0'
      webpack-hot-middleware: 2.x
    peerDependenciesMeta:
      webpack-hot-middleware:
        optional: true

  '@swc/helpers@0.5.17':
    resolution: {integrity: sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==}

  '@types/prop-types@15.7.14':
    resolution: {integrity: sha512-gNMvNH49DJ7OJYv+KAKn0Xp45p8PLl6zo2YnvDIbTd4J6MER2BmWN49TG7n9LvkyihINxeKW8+3bfS2yDC9dzQ==}

  '@types/react-dom@18.3.7':
    resolution: {integrity: sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==}
    peerDependencies:
      '@types/react': ^18.0.0

  '@types/react@18.3.23':
    resolution: {integrity: sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==}

  caniuse-lite@1.0.30001721:
    resolution: {integrity: sha512-cOuvmUVtKrtEaoKiO0rSc29jcjwMwX5tOHDy4MgVFEWiUXj4uBMJkwI8MDySkgXidpMiHUcviogAvFi4pA2hDQ==}

  core-js@3.42.0:
    resolution: {integrity: sha512-Sz4PP4ZA+Rq4II21qkNqOEDTDrCvcANId3xpIgB34NDkWc3UduWj2dqEtN9yZIq8Dk3HyPI33x9sqqU5C8sr0g==}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  error-stack-parser@2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==}

  html-entities@2.6.0:
    resolution: {integrity: sha512-kig+rMn/QOVRvr7c86gQ8lWXq+Hkv6CbAH1hLu+RG338StTpE8Z0b44SDVaqVu7HGKf27frdmUYEs9hTUX/cLQ==}

  jiti@2.4.2:
    resolution: {integrity: sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==}
    hasBin: true

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  react-dom@16.14.0:
    resolution: {integrity: sha512-1gCeQXDLoIqMgqD3IO2Ah9bnf0w9kzhwN5q4FGnHZ67hBm9yePzB5JJAIQCc8x3pFnNlwFq4RidZggNAAkzWWw==}
    peerDependencies:
      react: ^16.14.0

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-refresh@0.17.0:
    resolution: {integrity: sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==}
    engines: {node: '>=0.10.0'}

  react@16.14.0:
    resolution: {integrity: sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g==}
    engines: {node: '>=0.10.0'}

  scheduler@0.19.1:
    resolution: {integrity: sha512-n/zwRWRYSUj0/3g/otKDRPMh6qv2SYMWNq85IEa8iZyAv8od9zDYpGSnpBEjNgcMNq6Scbu5KfIPxNF72R/2EA==}

  stackframe@1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

snapshots:

  '@module-federation/error-codes@0.14.0': {}

  '@module-federation/runtime-core@0.14.0':
    dependencies:
      '@module-federation/error-codes': 0.14.0
      '@module-federation/sdk': 0.14.0

  '@module-federation/runtime-tools@0.14.0':
    dependencies:
      '@module-federation/runtime': 0.14.0
      '@module-federation/webpack-bundler-runtime': 0.14.0

  '@module-federation/runtime@0.14.0':
    dependencies:
      '@module-federation/error-codes': 0.14.0
      '@module-federation/runtime-core': 0.14.0
      '@module-federation/sdk': 0.14.0

  '@module-federation/sdk@0.14.0': {}

  '@module-federation/webpack-bundler-runtime@0.14.0':
    dependencies:
      '@module-federation/runtime': 0.14.0
      '@module-federation/sdk': 0.14.0

  '@rsbuild/core@1.3.22':
    dependencies:
      '@rspack/core': 1.3.12(@swc/helpers@0.5.17)
      '@rspack/lite-tapable': 1.0.1
      '@swc/helpers': 0.5.17
      core-js: 3.42.0
      jiti: 2.4.2

  '@rsbuild/plugin-react@1.3.2(@rsbuild/core@1.3.22)':
    dependencies:
      '@rsbuild/core': 1.3.22
      '@rspack/plugin-react-refresh': 1.4.3(react-refresh@0.17.0)
      react-refresh: 0.17.0
    transitivePeerDependencies:
      - webpack-hot-middleware

  '@rspack/binding-darwin-arm64@1.3.12':
    optional: true

  '@rspack/binding-darwin-x64@1.3.12':
    optional: true

  '@rspack/binding-linux-arm64-gnu@1.3.12':
    optional: true

  '@rspack/binding-linux-arm64-musl@1.3.12':
    optional: true

  '@rspack/binding-linux-x64-gnu@1.3.12':
    optional: true

  '@rspack/binding-linux-x64-musl@1.3.12':
    optional: true

  '@rspack/binding-win32-arm64-msvc@1.3.12':
    optional: true

  '@rspack/binding-win32-ia32-msvc@1.3.12':
    optional: true

  '@rspack/binding-win32-x64-msvc@1.3.12':
    optional: true

  '@rspack/binding@1.3.12':
    optionalDependencies:
      '@rspack/binding-darwin-arm64': 1.3.12
      '@rspack/binding-darwin-x64': 1.3.12
      '@rspack/binding-linux-arm64-gnu': 1.3.12
      '@rspack/binding-linux-arm64-musl': 1.3.12
      '@rspack/binding-linux-x64-gnu': 1.3.12
      '@rspack/binding-linux-x64-musl': 1.3.12
      '@rspack/binding-win32-arm64-msvc': 1.3.12
      '@rspack/binding-win32-ia32-msvc': 1.3.12
      '@rspack/binding-win32-x64-msvc': 1.3.12

  '@rspack/core@1.3.12(@swc/helpers@0.5.17)':
    dependencies:
      '@module-federation/runtime-tools': 0.14.0
      '@rspack/binding': 1.3.12
      '@rspack/lite-tapable': 1.0.1
      caniuse-lite: 1.0.30001721
    optionalDependencies:
      '@swc/helpers': 0.5.17

  '@rspack/lite-tapable@1.0.1': {}

  '@rspack/plugin-react-refresh@1.4.3(react-refresh@0.17.0)':
    dependencies:
      error-stack-parser: 2.1.4
      html-entities: 2.6.0
      react-refresh: 0.17.0

  '@swc/helpers@0.5.17':
    dependencies:
      tslib: 2.8.1

  '@types/prop-types@15.7.14': {}

  '@types/react-dom@18.3.7(@types/react@18.3.23)':
    dependencies:
      '@types/react': 18.3.23

  '@types/react@18.3.23':
    dependencies:
      '@types/prop-types': 15.7.14
      csstype: 3.1.3

  caniuse-lite@1.0.30001721: {}

  core-js@3.42.0: {}

  csstype@3.1.3: {}

  error-stack-parser@2.1.4:
    dependencies:
      stackframe: 1.3.4

  html-entities@2.6.0: {}

  jiti@2.4.2: {}

  js-tokens@4.0.0: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  object-assign@4.1.1: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  react-dom@16.14.0(react@16.14.0):
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1
      react: 16.14.0
      scheduler: 0.19.1

  react-is@16.13.1: {}

  react-refresh@0.17.0: {}

  react@16.14.0:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1

  scheduler@0.19.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1

  stackframe@1.3.4: {}

  tslib@2.8.1: {}

  typescript@5.8.3: {}
