# SW AI Federated Modules

this project is a collection of modules that can be used to build AI applications in a federated manner. 

The modules are designed to be used inside DingTalk OA and can be easily integrated into existing projects.

## Technology Stack

* react / react-dom 16
* typescript
* module-federation of rsbuild bundler
* use pnpm as package manager

## Dir Spec

* `package.json` check the [package.json] for list of dependencies and scripts.
* `src/entries` are for the federated modules, each module should have its own entry file.
* `src/components` are for the shared components
* `src/utils` are for the shared utilities
* `src/services` are for the shared services
* `src/types` are for types files