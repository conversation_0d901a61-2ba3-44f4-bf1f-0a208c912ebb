/**
 * Comprehensive TypeScript types for DingTalk OA Approval Form Schema
 * 
 * This module defines types for a federated form builder system that supports
 * various field types, layouts, and data sources for DingTalk OA applications.
 * 
 * <AUTHOR> AI Team
 * @version 1.0.0
 */

// ============================================================================
// Base Types and Interfaces
// ============================================================================

/**
 * Base properties that are common to all form components
 */
export interface BaseComponentProps {
  /** Unique identifier for the component */
  id: string;
  /** Business alias for backend processing */
  bizAlias?: string;
  /** Whether this field should be excluded from print view */
  notPrint?: "0" | "1" | string;
  /** Whether this field should be hidden in approval detail view */
  hiddenInApprovalDetail?: boolean;
}

/**
 * Properties for form input components that accept user input
 */
export interface FormInputProps extends BaseComponentProps {
  /** Display label for the field */
  label: string | [string, string];
  /** Whether the field is required */
  required?: boolean;
  /** Placeholder text */
  placeholder?: string;
  /** Default value for the field */
  defaultValue?: any;
  /** Field dependencies and relationships */
  rely?: Record<string, any>;
  /** Additional field configurations */
  fields?: any[];
  /** Data source configuration */
  dataSource?: DataSourceConfig;
}

/**
 * Layout and sizing properties
 */
export interface LayoutProps {
  /** Width ratio (percentage) */
  ratio?: number;
  /** Whether to spread options horizontally */
  spread?: boolean;
}

/**
 * Data source configuration for dynamic data loading
 */
export interface DataSourceConfig {
  /** Type of data source */
  type: "db_table" | "api" | "static";
  /** Target configuration for the data source */
  target?: {
    appUuid?: string;
    appId?: string;
    sourceForm?: string;
    scene?: string;
    initScene?: string;
  };
}

/**
 * Option interface for select components
 */
export interface SelectOption {
  /** Option key/value */
  key: string;
  /** Display value */
  value: string;
  /** Optional label (for extended value) */
  label?: string;
}

/**
 * Extended value structure for select components
 */
export interface ExtendedValue {
  key: string;
  label?: string;
}

/**
 * Style configuration for components
 */
export interface ComponentStyle {
  color?: string;
  fontSize?: string;
  fontWeight?: string;
  [key: string]: any;
}

// ============================================================================
// Component-Specific Prop Types
// ============================================================================

/**
 * Props for single-line text input field
 */
export interface TextFieldProps extends FormInputProps, LayoutProps {}

/**
 * Props for multi-line text input field
 */
export interface TextareaFieldProps extends FormInputProps, LayoutProps {}

/**
 * Props for number input field with unit and precision support
 */
export interface NumberFieldProps extends FormInputProps, LayoutProps {
  /** Unit symbol (e.g., "$", "€", "¥") */
  unit?: string;
  /** Decimal precision */
  precision?: number;
}

/**
 * Props for single-select dropdown field
 */
export interface DDSelectFieldProps extends FormInputProps, LayoutProps {
  /** Available options */
  options: SelectOption[];
  /** Behavior linkage configuration */
  behaviorLinkage?: any[];
  /** Extended default value with key-label mapping */
  defaultExtendValue?: ExtendedValue;
}

/**
 * Props for multi-select dropdown field
 */
export interface DDMultiSelectFieldProps extends FormInputProps, LayoutProps {
  /** Available options */
  options: SelectOption[];
  /** Behavior linkage configuration */
  behaviorLinkage?: any[];
  /** Extended default values array */
  defaultExtendValue?: ExtendedValue[];
}

/**
 * Props for date picker field
 */
export interface DDDateFieldProps extends FormInputProps {
  /** Date format (e.g., "yyyy-MM-dd", "yyyy-MM-dd HH:mm") */
  format: string;
  /** Time unit ("天", "小时", "分钟") */
  unit: string;
}

/**
 * Props for date range picker field
 */
export interface DDDateRangeFieldProps extends FormInputProps {
  /** Labels for start and end date [startLabel, endLabel] */
  label: [string, string];
  /** Date format */
  format: string;
  /** Time unit */
  unit: string;
  /** Whether to show duration calculation */
  duration?: boolean;
  /** Label for duration display */
  durationLabel?: string;
}

/**
 * Props for ID card input field with masking support
 */
export interface IdCardFieldProps extends FormInputProps {
  /** Whether to mask sensitive information */
  mask?: boolean;
}

/**
 * Phone field modes
 */
export type PhoneMode = "phone" | "tel" | "phone_tel";

/**
 * Props for phone number input field
 */
export interface PhoneFieldProps extends FormInputProps {
  /** Input mode: phone, tel, or both */
  mode: PhoneMode;
  /** Whether to mask the phone number */
  mask?: boolean;
}

/**
 * Props for money input field
 */
export interface MoneyFieldProps extends FormInputProps {
  /** Whether to show uppercase amount in Chinese */
  notUpper?: "0" | "1";
  /** Decimal precision */
  precision?: number;
}

/**
 * Props for photo upload field
 */
export interface DDPhotoFieldProps extends FormInputProps {
  /** Whether to add watermark to photos */
  watermark: boolean;
  /** Maximum number of photos allowed */
  maxUpload?: number;
}

/**
 * Props for cascading selection field
 */
export interface CascadeFieldProps extends FormInputProps {
  /** Alternative spelling for required */
  require?: boolean;
}

/**
 * Props for table field with nested components
 */
export interface TableFieldProps extends BaseComponentProps {
  /** Table label */
  label: string;
  /** Text for add button */
  actionName: string;
  /** View mode for the table */
  tableViewMode: "table" | "card";
}

/**
 * Props for file attachment field
 */
export interface DDAttachmentProps extends FormInputProps {
  /** Application ID */
  appId?: string;
  /** Whether pre-signing is needed */
  needPreSign?: boolean;
  /** Whether e-signature is enabled */
  eSign?: boolean;
  /** Whether DingTalk e-signature is enabled */
  dingEsign?: boolean;
  /** Custom attributes */
  customAttrs?: {
    dingEsign?: boolean;
    [key: string]: any;
  };
}

/**
 * Props for internal contact selection field
 */
export interface InnerContactFieldProps extends FormInputProps {
  /** Selection mode: "0" = single, "1" = multiple */
  choice: "0" | "1";
}

/**
 * Props for external contact field
 */
export interface ExternalContactFieldProps extends FormInputProps {}

/**
 * Props for department selection field
 */
export interface DepartmentFieldProps extends BaseComponentProps {
  /** Field label */
  label: string;
  /** Placeholder text */
  placeholder: string;
  /** Whether multiple selection is allowed */
  multiple: boolean;
  /** Whether the field is required */
  required: boolean;
  /** Whether to show full department name path */
  showDeptFullName: boolean;
}

/**
 * Props for time and location capture field
 */
export interface TimeAndLocationFieldProps extends BaseComponentProps {
  /** Whether the field is required */
  required: boolean;
  /** Labels for time and location [timeLabel, locationLabel] */
  label: [string, string];
}

/**
 * Address detail level type
 */
export type AddressDetailLevel = boolean | 0 | 1;

/**
 * Props for address selection field
 */
export interface AddressFieldProps extends FormInputProps {
  /** Level of detail needed: true/1 = with street, false/0 = without street */
  needDetail: AddressDetailLevel;
}

/**
 * Props for star rating field
 */
export interface StarRatingFieldProps extends FormInputProps {
  /** Maximum rating limit (5 or 10) */
  limit: 5 | 10;
}

/**
 * Props for text note/description component
 */
export interface TextNoteProps extends BaseComponentProps {
  /** Text content to display */
  content: string;
  /** Optional link URL */
  link?: string;
  /** Style configuration */
  style?: ComponentStyle;
}

/**
 * Props for column layout component
 */
export interface ColumnLayoutProps extends BaseComponentProps {
  /** Layout label */
  label: string;
  /** Array of component IDs to group in this layout */
  group: string[];
}

// ============================================================================
// Component Union Types
// ============================================================================

/**
 * All available component names in the form schema
 */
export type ComponentName =
  | "TextField"
  | "TextareaField"
  | "NumberField"
  | "DDSelectField"
  | "DDMultiSelectField"
  | "DDDateField"
  | "DDDateRangeField"
  | "IdCardField"
  | "PhoneField"
  | "MoneyField"
  | "DDPhotoField"
  | "CascadeField"
  | "TableField"
  | "DDAttachment"
  | "InnerContactField"
  | "ExternalContactField"
  | "DepartmentField"
  | "TimeAndLocationField"
  | "AddressField"
  | "StarRatingField"
  | "TextNote"
  | "ColumnLayout";

/**
 * Discriminated union of all form components using componentName as discriminator
 * This ensures type safety when working with different component types
 */
export type FormComponent =
  | { componentName: "TextField"; props: TextFieldProps }
  | { componentName: "TextareaField"; props: TextareaFieldProps }
  | { componentName: "NumberField"; props: NumberFieldProps }
  | { componentName: "DDSelectField"; props: DDSelectFieldProps }
  | { componentName: "DDMultiSelectField"; props: DDMultiSelectFieldProps }
  | { componentName: "DDDateField"; props: DDDateFieldProps }
  | { componentName: "DDDateRangeField"; props: DDDateRangeFieldProps }
  | { componentName: "IdCardField"; props: IdCardFieldProps }
  | { componentName: "PhoneField"; props: PhoneFieldProps }
  | { componentName: "MoneyField"; props: MoneyFieldProps }
  | { componentName: "DDPhotoField"; props: DDPhotoFieldProps }
  | { componentName: "CascadeField"; props: CascadeFieldProps }
  | { componentName: "TableField"; props: TableFieldProps; children?: FormComponent[] }
  | { componentName: "DDAttachment"; props: DDAttachmentProps }
  | { componentName: "InnerContactField"; props: InnerContactFieldProps }
  | { componentName: "ExternalContactField"; props: ExternalContactFieldProps }
  | { componentName: "DepartmentField"; props: DepartmentFieldProps }
  | { componentName: "TimeAndLocationField"; props: TimeAndLocationFieldProps }
  | { componentName: "AddressField"; props: AddressFieldProps }
  | { componentName: "StarRatingField"; props: StarRatingFieldProps }
  | { componentName: "TextNote"; props: TextNoteProps }
  | { componentName: "ColumnLayout"; props: ColumnLayoutProps };

// ============================================================================
// Main Schema Types
// ============================================================================

/**
 * Complete form schema structure
 * Contains an array of form components that define the entire form
 */
export interface FormSchema {
  /** Array of form components */
  schema: FormComponent[];
}

/**
 * Form data structure for storing user input values
 * Keys correspond to component IDs, values are the user input
 */
export interface FormData {
  [componentId: string]: any;
}

/**
 * Form validation result
 */
export interface ValidationResult {
  /** Whether validation passed */
  isValid: boolean;
  /** Field-specific error messages */
  errors: Record<string, string>;
  /** General form-level warnings */
  warnings?: string[];
}

/**
 * Form submission result
 */
export interface SubmissionResult {
  /** Whether submission was successful */
  success: boolean;
  /** Submission ID or reference */
  submissionId?: string;
  /** Error message if submission failed */
  error?: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Extract props type for a specific component
 * Usage: ComponentProps<"TextField"> returns TextFieldProps
 */
export type ComponentProps<T extends ComponentName> = Extract<
  FormComponent,
  { componentName: T }
>["props"];

/**
 * Type guard to check if a component is a specific type
 */
export function isComponentType<T extends ComponentName>(
  component: FormComponent,
  componentName: T
): component is Extract<FormComponent, { componentName: T }> {
  return component.componentName === componentName;
}

/**
 * Type guard to check if a component has children (container component)
 */
export function hasChildren(
  component: FormComponent
): component is Extract<FormComponent, { children?: FormComponent[] }> {
  return "children" in component;
}

// ============================================================================
// Constants and Enums
// ============================================================================

/**
 * Supported date formats
 */
export const DATE_FORMATS = {
  DATE_ONLY: "yyyy-MM-dd",
  DATETIME: "yyyy-MM-dd HH:mm",
  TIME_ONLY: "HH:mm",
} as const;

/**
 * Supported time units
 */
export const TIME_UNITS = {
  DAY: "天",
  HOUR: "小时",
  MINUTE: "分钟",
} as const;

/**
 * Phone input modes
 */
export const PHONE_MODES = {
  MOBILE: "phone",
  LANDLINE: "tel", 
  BOTH: "phone_tel",
} as const;

/**
 * Star rating limits
 */
export const RATING_LIMITS = {
  FIVE_STAR: 5,
  TEN_STAR: 10,
} as const;
