## 目录结构

```
.
├── gulpfile.js  ---------------------- gulp入口文件
├── tslint.json  ---------------------- tslint代码风格检查配置
├── tsconfig.json  -------------------- ts编译配置
├── package.json  --------------------- 项目配置
├── README.md  ------------------------ 说明文件
└── src  ------------------------------ 源码目录
    ├── base  ----------------------------- 核心基础库
    │   ├── fetch.js  ------------------------- 通用请求工具(http/lwp)
    │   └── ...  ------------------------------ 更多其它基础工具
    ├── components  ----------------------- 基础项目组件
    │   ├── MField   -------------------------- 编辑态表单基础布局组件
    │   ├── MFieldView ------------------------ 只读态表单基础布局组件
    │   └── ... ------------------------------- 更多其它组件
    ├── engine  --------------------------- 渲染引擎
    │   ├── Context.tsx  ---------------------- 渲染类，得到当前schema上下文
    │   ├── FieldStore.ts  --------------------- 组件数据类，管理每个组件的状态及数据
    │   ├── Validator.ts  --------------------- 检验引擎
    │   └── Wrapper.tsx  ---------------------- 组件视图包裹，视图渲染前做一些容错等额外操作
    ├── plugins  -------------------------- 渲染插件，在渲染
    │   ├── formulaPlugin.ts  ----------------- 公式插件，解析schema给组件绑定公式事件
    │   └── ...  ------------------------------ 其它更多插件
    ├── trunk  ---------------------------- 核心组件库
    │   ├── DateField  ----------------- e.g. 日期组件
    │   │   ├── view.mobile.tsx  ------------------ 移动端钉钉内webview视图(默认视图)
    │   │   ├── view.h5.tsx  ---------------------- 移动端浏览器视图
    │   │   ├── view.web.tsx  --------------------- PC视图
    │   │   └── ...  ------------------------------ 其它(暂不作使用)
    │   └── ...  ------------------------------ 其它更多插件
    └── utils  ---------------------------- 其它工具包(迁移遗留)
```

## 开始开发

1. tnpm install
2. tnpm start

默认为移动版渲染地址 `http://test.dingtalk.com:8000/demo/preview.html` 

PC版增加参数`pc`或`web`, 如 `http://test.dingtalk.com:8000/demo/preview.html?pc` 

详情版地址增加参数`viewMode`，如 `http://test.dingtalk.com:8000/demo/preview.html?viewMode`

## jsapi测试
jsapi调用存在白名单
### 方式一 使用开发包
开发包中打开调试模式，中使用ip访问即可

### 方式二 绑定dingtalk.com白名单地址
1. 绑定host，`127.0.0.1 test.dingtalk.com`
2. 钉钉访问 `http://test.dingtalk.com:8000/demo/preview.html` 


## 使用