import RenderEngine from '../src/engine';
import trunkMobile from '../src/trunk.mobile';

const ua = (navigator.userAgent || navigator.swuserAgent);
const isDDPC = ua.indexOf('dingtalk-win') > -1;
let _ddVersion = ua.match(/DingTalk\/([\d\.]+)/);
// windows版的ua是
// "Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/49.0.2623.110 Safari/537.36 dingtalk-win/1.0.0 nw(0.14.7) DingTalk(4.5.21) Mojo/1.0.0 Native AppType(beta) Nebula"
// 以上正则取不到，用以下正则去取(考虑到桌面版钉钉会有beta或者rc这种类型的版本号)
// mac的ua不用下面的正则去取
if (isDDPC && !_ddVersion) {
  _ddVersion = ua.match(/DingTalk\(([a-zA-Z0-9\.-]+)\)/);
}
const ddSystemVersion = _ddVersion && _ddVersion[1] || '100.0.0';



export default function createPage(opt) {
    const viewMode = opt.viewMode
    const params = opt.params || {};
    const formSchema = opt.formSchema || {};
    const formData = opt.formData || {};
    if (viewMode) {
      document.body.classList.add('viewMode');
    }else{
      document.body.classList.remove('viewMode');
    }
    return RenderEngine.Page({
      viewMode,
      params,
      schema: formSchema,
      data: formData,
      components: trunkMobile,
    });
}

self.AFAppX.addExtraComponent({
    name: 'super-form',
    factory: function(params) {
      return params.createReactClass({
        displayName: 'SuperForm',
        getFormData(callback) {
          callback && callback(this.refs.formContext.getFormData());
        },
        getFormDataMap(callback) {
          callback && callback(this.refs.formContext.getFormDataMap());
        },
        isFormValid(callback) {
          callback && callback(this.refs.formContext.isFormValid());
        },
        render() {
          const { className, id, options } = this.props;
          console.log('ExtraComponent options', options);
          return (
            <div className={className} id={id}>
              {createPage(options)}
            </div>
          );
        },
      });
    }
});

(function(){
  var dd = self.dd || {};
  dd.version = ddSystemVersion;
  dd.pc = isDDPC;
  var jsApiList = [
    'device.notification.alert',
    'device.notification.confirm',
    'device.notification.toast',
    'device.notification.prompt',
    'device.notification.actionSheet',
    'device.notification.showPreloader',
    'device.notification.hidePreloader',
    'biz.util.open',
    'biz.util.openModal',
    'biz.util.openSlidePanel',
    'biz.util.chosen',
    'biz.util.datepicker',
    'biz.util.datetimepicker',
    'biz.util.multiSelect',
    'biz.util.uploadImage',
    'biz.util.uploadImageFromCamera',
    'biz.util.downloadFile',
    'biz.util.openLocalFile',
    'biz.util.isLocalFileExist',
    'biz.util.previewImage',
    'biz.util.timestamp',
    'biz.util.openLink',
    'biz.util.ut',
    'biz.util.previewFile',
    'biz.util.uploadAttachment',
    'biz.cspace.preview',
    'biz.contact.choose',
    'biz.contact.departmentsPicker',
    'biz.contact.externalComplexPicker',
    'biz.contact.externalEditForm',
    'biz.contact.complexPicker',
    'biz.calendar.chooseHalfDay',
    'biz.calendar.chooseDateTime',
    'biz.calendar.chooseOneDay',
    'biz.calendar.datePicker',
    'biz.map.view',
    'internal.request.lwp'
  ];

  dd.dtBridge = function(opt) {
    const m = opt.m;
    var onSuccess = opt.onSuccess;
    var onFail = opt.onFail;
    delete opt.onSuccess;
    delete opt.onFail;
    const mArr = m.split('.');
    const actionName = mArr.pop() || '';
    const serviceName = mArr.join('.');

    dd.call('ddExec', {
      actionName,
      serviceName,
      args: opt.args,
    }, (res) => {
      if (!res || !res.success) {
        onFail && onFail();
        reject();
        return;
      }
      try {
        const content = JSON.parse(res.content);
        onSuccess && onSuccess(content);
      } catch(e) {
        onFail && onFail(e);
      }
    });
  }

  dd.compareVersion = !dd.version || dd.pc ? () => true : function (e,n,a){if("string"!=typeof e||"string"!=typeof n)return!1;for(var i,o,t=e.split("."),r=n.split(".");i===o&&r.length>0;)i=t.shift(),o=r.shift();return a ?(0|o)>=(0|i) :(0|o) > (0 | i); };

  for (var i = 0; i < jsApiList.length; i++) {
    var jsapi = jsApiList[i];
    var arrs = jsapi.split('.');
    
    var last = dd;
    for (var j = 0; j < arrs.length; j++) {
      var aarr = arrs[j];
      if (j == arrs.length - 1) {
        last[aarr] = (function(m) {
          return function(opt) {
            var onSuccess = opt.onSuccess;
            var onFail = opt.onFail;
            delete opt.onSuccess;
            delete opt.onFail;
            const mArr = m.split('.');
            const actionName = mArr.pop() || '';
            const serviceName = mArr.join('.');
            return new Promise((resolve, reject) => {
              dd.call('ddExec', {
                actionName,
                serviceName,
                args: opt,
              }, (res) => {
                if (!res || !res.success) {
                  onFail && onFail();
                  reject();
                  return;
                }
                try {
                  const content = JSON.parse(res.content);
                  onSuccess && onSuccess(content);
                  resolve(content);
                } catch(e) {
                  onFail && onFail(e);
                  reject(e);
                }
              });
            })
          }
        })(jsapi)
      } else {
        last[aarr] = last[aarr] || {};
      }
      
      last = last[aarr];
    }
  }
})();