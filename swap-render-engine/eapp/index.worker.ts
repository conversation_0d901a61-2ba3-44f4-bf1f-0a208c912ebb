(function(){
    const dd = self.dd || {};
    dd.createSuperFormContext = function(id) {
      return new SuperFormContext(id, self.AFAppX.getCurrentPageImpl());
    }
  
    function SuperFormContext(id, page) {
      this.id = id;
      this.page = page;
    }
  
    const proto = SuperFormContext.prototype;
  
    [
      'getFormData',
      'getFormDataMap',
      'isFormValid',
    ].forEach(method => {
      proto[method] = function run(...args) {
        this.page.callRemote(
          `refComponents.${this.id}`,
          method,
          ...args
        );
      };
    });
})();
    