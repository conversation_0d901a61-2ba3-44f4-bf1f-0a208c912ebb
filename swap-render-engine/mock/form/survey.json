[{"componentName": "DDSelectField", "componentOptions": "[{\"extension\":{\"image\":\"https://static.dingtalk.com/media/lQLPM5y6Fxqno2jNBDjNBB2w8fXMIGAp8boDnk3GPgDqAA_1053_1080.png\"},\"value\":\"选项1\",\"key\":\"option_0\"},{\"value\":\"选项2\",\"key\":\"option_1\"},{\"value\":\"选项3\",\"key\":\"option_2\"},{\"extension\":{\"image\":\"\"},\"value\":\"其它\",\"key\":\"other\"}]", "props": {"tag": "单选框", "tagColor": "#FF943E", "options": [{"extension": {"image": "https://static.dingtalk.com/media/lQLPM5y6Fxqno2jNBDjNBB2w8fXMIGAp8boDnk3GPgDqAA_1053_1080.png"}, "value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}, {"extension": {"image": ""}, "value": "其它", "key": "other"}], "label": "单选题 我的标题很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长", "placeholder": "请选择", "id": "DDSelectField_LBX3F93Y", "required": true, "allowClear": true, "spread": true, "behaviorLinkage": [{"value": "option_0", "targets": [{"behavior": "NORMAL", "fieldId": "DDDateField_LBX3OFXJ"}]}, {"value": "option_1", "targets": [{"behavior": "NORMAL", "fieldId": "NumberField_LBX3P0BM"}]}]}}, {"componentName": "DDDateField", "props": {"unit": "日)", "format": "yyyy-MM-dd", "label": "选项1关联出来的", "placeholder": "请选择", "id": "DDDateField_LBX3OFXJ", "required": false}}, {"componentName": "NumberField", "props": {"unit": "", "label": "选项2关联出来的", "placeholder": "请输入数字", "id": "NumberField_LBX3P0BM", "required": false}}, {"componentName": "DDMultiSelectField", "componentOptions": "[{\"extension\":{\"image\":\"https://static.dingtalk.com/media/lQLPM5XCkkeJH-jNBDjNBB2w9QKP2Q6syAgDnk2_OACfAA_1053_1080.png\"},\"value\":\"选项1\",\"key\":\"option_0\"},{\"value\":\"选项2\",\"key\":\"option_1\"},{\"value\":\"选项3\",\"key\":\"option_2\"},{\"extension\":{\"image\":\"\"},\"value\":\"其它\",\"key\":\"other\"}]", "props": {"tag": "多选框", "tagColor": "#3296FA", "options": [{"extension": {"image": "https://static.dingtalk.com/media/lQLPM5XCkkeJH-jNBDjNBB2w9QKP2Q6syAgDnk2_OACfAA_1053_1080.png"}, "value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}, {"extension": {"image": ""}, "value": "其它", "key": "other"}], "label": "多选题 我的标题很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长", "placeholder": "请选择", "id": "DDMultiSelectField_LBX3F9K5", "required": false, "allowClear": true, "spread": true}}, {"componentName": "TextareaField", "props": {"label": "问答题", "placeholder": "请输入111", "id": "TextareaField_LBX3F9XL", "required": false}}, {"componentName": "StarRatingField", "props": {"limit": 10, "label": "请进行评分10分制", "placeholder": "请输入", "id": "StarRatingField_LBX3FAA3", "required": false}}, {"componentName": "StarRatingField", "props": {"limit": 5, "label": "请进行评分", "placeholder": "请输入", "id": "StarRatingField_LBX3MW44", "required": true}}, {"componentName": "PhoneField", "props": {"mode": "phone", "label": "电话-有格式校验", "placeholder": "请输入", "id": "PhoneField_LBX3FANJ", "needValidation": false, "required": false}}, {"componentName": "IdCardField", "props": {"label": "身份证", "placeholder": "请输入", "id": "IdCardField_LBX3FB0Y", "required": false}}, {"componentName": "TableField", "children": [{"componentName": "DDPhotoField", "props": {"watermark": false, "bizParentId": "TravelCard_LBX3FBTH", "label": "通信行程卡截图", "id": "DDPhotoField_screenshot", "useAlbum": true, "required": false}}, {"componentName": "DDSelectField", "props": {"bizParentId": "TravelCard_LBX3FBTH", "options": [{"value": "非绿卡", "key": "notgreen"}, {"value": "绿卡", "key": "green"}], "label": "行程卡状态", "placeholder": "请选择", "id": "DDSelectField_code", "required": false, "spread": true}}], "props": {"id": "TravelCard_LBX3FBTH", "label": "通信行程卡", "screenshotDays": 1, "required": true}}, {"componentName": "NumberField", "props": {"unit": "元", "label": "数字-带单位", "placeholder": "请输入数字", "id": "NumberField_LBX3LZV4", "required": true}}, {"componentName": "SignatureField", "props": {"readFromLast": true, "label": "手写签名", "id": "SignatureField_LBX3FCIP", "required": true}}, {"componentName": "SignatureField", "props": {"readFromLast": true, "label": "手写签名手写签名-需要重新签名", "id": "SignatureField_LBX3LJBX"}}, {"componentName": "DDAttachment", "props": {"subLabel": "只有发起人可以查看提交的文件", "templates": [], "label": "文件上传", "id": "DDAttachment_LBX3FD3Z", "required": true, "spread": true}}, {"componentName": "DDAttachment", "props": {"subLabel": "只有发起人可以查看提交的文件", "templates": [{"spaceId": "4110965773", "fileName": "Simulator Screen Shot - iPhone 12 - 2022-12-14 at 16.07.59 #2.png", "thumbnail": {"authCode": "", "rotation": 0, "authMediaId": "$iAElAqNqcGcDAQTNAR4FzQJsBtoAI4QBpCErz-QCqghnhK7yBYqqL6IDzwAAAYUyvBwxBM4AAZe0BwAIAA", "width": 1170, "mediaId": "", "height": 2532}, "fileSize": "337607", "fileType": "png", "fileId": "92771620454"}], "label": "文件上传-含模板文件", "id": "DDAttachment_LBX3KXCM", "required": true, "spread": true}}, {"componentName": "MatrixRadioField", "props": {"label": "表格单选", "id": "MatrixRadioField_LBX3FDH3", "rows": [{"name": "行1", "alias": "", "key": "row_1"}, {"name": "行2", "alias": "", "key": "row_LBX3KG2F"}], "cols": [{"name": "列", "alias": "", "key": "col_1"}, {"name": "列2", "alias": "", "key": "col_LBX3KGPS"}], "required": true}}, {"componentName": "MatrixTextField", "props": {"label": "表格填空", "id": "MatrixTextField_LBX3FDWG", "rows": [{"name": "行1", "alias": "", "key": "row_1"}, {"name": "行2", "alias": "", "key": "row_LBX3KDNT"}], "cols": [{"name": "列", "alias": "", "key": "col_1"}, {"name": "列2", "alias": "", "key": "col_LBX3KEBU"}], "required": true}}, {"componentName": "AddressField", "props": {"needDetail": false, "label": "省市区", "id": "AddressField_LBX3FEJ9", "required": true}}, {"componentName": "AddressField", "props": {"needDetail": true, "label": "省市区带街道", "id": "AddressField_LBX3K1JH", "required": false}}, {"componentName": "TimeAndLocationField", "props": {"label": ["当前时间", "当前位置"], "id": "TimeAndLocationField_LBX3FF2Q", "required": false}}, {"componentName": "TextNote", "props": {"watch": false, "link": "", "id": "TextNote_LBX3FFFQ", "content": "说明文字"}}, {"componentName": "TextNote", "props": {"watch": false, "link": "https://www.baidu.com/", "id": "TextNote_LBX3JK5O", "content": "说明文字跳转到百度"}}, {"componentName": "DDDateField", "props": {"unit": "日)", "format": "yyyy-MM-dd", "label": "日期", "placeholder": "请选择", "id": "DDDateField_LBX3FGEQ", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "日)", "format": "yyyy-MM-dd HH:mm", "label": "日期带时分", "placeholder": "请选择", "id": "DDDateField_LBX3J8RX", "required": false}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "label": "图片", "id": "DDPhotoField_LBX3FGY4", "useAlbum": true, "required": true}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "label": "图片只允许拍照", "id": "DDPhotoField_LBX3IVW4", "useAlbum": false, "required": false}}, {"componentName": "DepartmentField", "props": {"multiple": false, "label": "部门", "id": "DepartmentField_LBX3FHD7", "required": true}}, {"componentName": "DepartmentField", "props": {"multiple": true, "label": "部门多选", "id": "DepartmentField_LBX3INMF", "required": false}}, {"componentName": "InnerContactField", "props": {"label": "联系人", "placeholder": "", "id": "InnerContactField_LBX3FI2D", "choice": 0, "required": true}}, {"componentName": "InnerContactField", "props": {"label": "联系人多选", "placeholder": "", "id": "InnerContactField_LBX3IF7W", "choice": true, "required": false}}, {"componentName": "NumberField", "props": {"unit": "", "label": "数字", "placeholder": "请输入数字", "id": "NumberField_LBX46Q40", "required": false}}, {"componentName": "CalculateField", "props": {"formula": [{"id": "NumberField_LBX46Q40"}, "*", 2], "label": "计算公式", "placeholder": "自动计算数值", "id": "CalculateField_LBX3FILI"}}, {"componentName": "HiddenField", "props": {"bizAlias": "interval", "id": "HiddenField_interval", "required": false}}]