[{"children": [{"componentName": "FormRelateField", "props": {"securityMode": 0, "bizAlias": "", "label": "测试一下1", "title": "", "required": false, "multi": 0, "quote": 0, "extract": true, "displayExtract": true, "id": "FormRelateField_GVGA3WXVIK00", "fields": [{"componentName": "TextField", "props": {"_extractId": "TextField_21FMW3T7HE3K0", "label": "单行输入框", "placeholder": "请输入", "id": "TextField_21FMW3T7HE3K0", "required": false, "ratio": 50, "_oriId": "TextField_1YGTVEM7AAF40"}}, {"componentName": "NumberField", "props": {"_extractId": "NumberField_TA4QDLMKGWW0", "label": "数字输入框", "placeholder": "请输入数字", "id": "NumberField_TA4QDLMKGWW0", "required": false, "ratio": 50, "_oriId": "NumberField_1BPHI3UMD8N40"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-749B7189-7176-471B-8C13-8F190E09ECA7", "appType": 0}}, "procType": ""}}, {"componentName": "TextField", "props": {"label": "单行输入框", "placeholder": "请输入", "id": "TextField_15EQQL9REO3G0", "required": false, "ratio": 50}}], "componentName": "TableField", "props": {"tableViewMode": "table", "label": "表格", "id": "TableField_1FB56T5R2H400", "actionName": "添加"}}]