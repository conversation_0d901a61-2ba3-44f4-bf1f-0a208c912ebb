export default [
  {
    "componentName": "DepartmentField",
    "props": {
      "multiple": false,
      "label": "部门",
      "placeholder": "请选择",
      "id": "DepartmentField_1FKS6C081BR40",
      "required": false
    }
  },
  {
    "children": [
      {
        "componentName": "TextField",
        "props": {
          "bizAlias": "",
          "label": "客户名称关键字",
          "placeholder": "请输入",
          "id": "TextField_20HQBXTBF9I80",
          "required": false,
          "ratio": 50
        }
      },
      {
        "componentName": "DDSelectField",
        "props": {
          "options": [
            {
              "value": "选项1",
              "key": "option_0"
            },
            {
              "value": "选项2",
              "key": "option_1"
            },
            {
              "value": "选项3",
              "key": "option_2"
            }
          ],
          "bizAlias": "",
          "label": "客户列表",
          "placeholder": "请选择",
          "id": "DDSelectField_1NJSLEP66N400",
          "required": false,
          "spread": false,
          "ratio": 50
        }
      },
      {
        "componentName": "TextField",
        "props": {
          "bizAlias": "",
          "label": "客户账期",
          "placeholder": "请输入",
          "id": "TextField_23LU69TUVA3K0",
          "required": false,
          "ratio": 50
        }
      }
    ],
    "componentName": "TableField",
    "props": {
      "tableViewMode": "table",
      "label": "表格",
      "id": "TableField_1AEIZ2IZPI8W0",
      "actionName": "添加"
    }
  },
  {
    "componentName": "DDMultiSelectField",
    "props": {
      "rely": {
        "type": "async"
      },
      "options": [],
      "bizAlias": "",
      "label": "多选框",
      "placeholder": "请选择",
      "id": "DDMultiSelectField_1U1XPCO9PA8W0",
      "fields": [
        {
          "suiteId": "",
          "hidden": false,
          "children": [],
          "opList": [],
          "dataType": "String",
          "suiteBizAlias": "",
          "suiteBizType": "",
          "componentName": "TextField",
          "displayPosition": "",
          "formField": true,
          "props": {
            "bizType": "",
            "statField": [],
            "link": "",
            "align": "",
            "durationLabel": "",
            "required": true,
            "content": "",
            "holidayOptions": [],
            "quote": "",
            "options": [],
            "id": "TextField-K2U5DHAA",
            "displayGroupBy": "",
            "placeHolder": "请输入组织全称",
            "behaviorLinkage": [],
            "objOptions": [],
            "notUpper": "",
            "format": "",
            "bizAlias": "customer_name",
            "label": "客户名称",
            "unit": "",
            "mainTitle": "",
            "notPrint": "",
            "fields": [],
            "attendTypeLabel": "",
            "actionName": ""
          }
        }
      ],
      "dataSource": {
        "type": "form",
        "params": {
          "filters": []
        },
        "target": {
          "appUuid": "SWAPP-47EF53D5C871BB79A14FE35858A67512",
          "formCode": "PROC-6A23B4EF-460D-4DB2-B481-4CA70F3D681C"
        }
      },
      "required": false,
      "spread": false,
      "ratio": 50,
      "behaviorLinkage": []
    }
  }
]
;
