[{"componentName": "TextField", "props": {"bizAlias": "", "label": "班级名称", "placeholder": "请输入", "id": "TextField_15EF0RC2WRXC0", "required": true, "ratio": 50}}, {"componentName": "TextField", "props": {"rely": {"formula": "", "type": "rely", "fields": ["TextField_15EF0RC2WRXC0"], "behavior": "readonly"}, "defaultValue": "", "bizAlias": "", "label": "学生", "placeholder": "请输入", "id": "TextField_1WNUUY72OAGW0", "fields": [{"props": {"id": "TextField_1HMHAE06GXXC0"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "TextField_15EF0RC2WRXC0", "fieldId": "TextField_1P7ICXPC7ZJ40"}]}, "target": {"appUuid": "SWAPP-634E25C1EDAC55FBB9D5706A2AFC6F9E", "formCode": "PROC-1B0CA38C-3B92-4BA7-8B88-A1319BA5D6BB"}}, "required": false, "ratio": 50}}, {"componentName": "FormRelateField", "props": {"quote": 0, "extract": true, "bizAlias": "", "label": "学生表", "id": "FormRelateField_18LH0A92VRS00", "title": "", "fields": [{"componentName": "TextField", "props": {"bizAlias": "", "label": "姓名", "placeholder": "请输入", "id": "TextField_1HMHAE06GXXC0", "required": false, "ratio": 50}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}], "label": "单选框", "placeholder": "请选择", "id": "DDSelectField_13VS7ALG7ZWG0", "required": false, "spread": false, "ratio": 50}}, {"componentName": "DDSelectField", "props": {"rely": {}, "options": [{"value": "男", "key": "option_0"}, {"value": "女", "key": "option_1"}], "bizAlias": "", "label": "性别", "placeholder": "请选择", "id": "DDSelectField_RMZCLDX4K400", "fields": [], "dataSource": {}, "required": false, "spread": false, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "NumberField", "props": {"bizAlias": "", "label": "年龄", "placeholder": "请输入数字", "id": "NumberField_1DJ8A5GIUFK00", "required": false, "ratio": 50}}, {"componentName": "TextField", "props": {"bizAlias": "", "label": "所在班级", "placeholder": "请输入", "id": "TextField_1P7ICXPC7ZJ40", "required": false, "ratio": 50}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "SWAPP-634E25C1EDAC55FBB9D5706A2AFC6F9E", "bizType": "", "formCode": "PROC-1B0CA38C-3B92-4BA7-8B88-A1319BA5D6BB", "appType": 1}}, "required": true, "multi": 0}}, {"componentName": "NumberField", "props": {"rely": {"formula": "", "type": "rely", "fields": ["FormRelateField_18LH0A92VRS00"], "behavior": "readonly"}, "defaultValue": "", "bizAlias": "", "label": "学生年龄", "placeholder": "请输入数字", "id": "NumberField_16JZ6JF8MDS00", "fields": [{"props": {"id": "NumberField_1DJ8A5GIUFK00"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "FormRelateField_18LH0A92VRS00", "fieldId": "id"}]}, "target": {"appUuid": "SWAPP-634E25C1EDAC55FBB9D5706A2AFC6F9E", "formCode": "PROC-1B0CA38C-3B92-4BA7-8B88-A1319BA5D6BB"}}, "required": false, "ratio": 50}}, {"componentName": "DDSelectField", "props": {"defaultValue": "", "bizAlias": "", "label": "学生性别", "required": false, "spread": false, "rely": {"formula": "", "type": "rely", "fields": ["FormRelateField_18LH0A92VRS00"], "behavior": "readonly"}, "options": [], "placeholder": "请选择", "id": "DDSelectField_1IDK2YL5BZPC0", "fields": [{"props": {"id": "DDSelectField_RMZCLDX4K400"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "FormRelateField_18LH0A92VRS00", "fieldId": "id"}]}, "target": {"appUuid": "SWAPP-634E25C1EDAC55FBB9D5706A2AFC6F9E", "formCode": "PROC-1B0CA38C-3B92-4BA7-8B88-A1319BA5D6BB"}}, "ratio": 50, "behaviorLinkage": []}}]