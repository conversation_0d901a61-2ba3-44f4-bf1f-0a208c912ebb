{"DDPhotoField_S140JWTVHYO0": {"mask": false, "value": "[\"http://i01.lw.aliimg.com/media/lALPDiQ3XtNcYADNAbjNAco_458_440.png\"]"}, "TableField_1ZHU6T8FWPPC0": {"mask": false, "extendValue": "{\"statValue\":[],\"componentName\":\"TableField\"}", "value": "[{\"rowNumber\":\"TableField_1ZHU6T8FWPPC0_PDDHA28A3R40\",\"rowValue\":[{\"key\":\"DDPhotoField_LF7M6WM1SRK0\",\"label\":\"图片\",\"mask\":false,\"value\":\"[\\\"http://i01.lw.aliimg.com/media/lADPDgtY6FHu4ALNAnHNAfQ_500_625.jpg\\\"]\"}]}]"}, "TableField_1G8BK2ZAAUOW0": {"mask": false, "extendValue": "{\"statValue\":[],\"componentName\":\"TableField\"}", "value": "[{\"rowNumber\":\"TableField_1G8BK2ZAAUOW0_OF34V99HDYO0\",\"rowValue\":[{\"key\":\"DDPhotoField_1EP873UR7C000\",\"label\":\"图片\",\"mask\":false,\"value\":\"[\\\"http://i01.lw.aliimg.com/media/lADPDhmO4uUCpkDNAVvNAbg_440_347.jpg\\\"]\"}]}]"}, "RecipientAccountField_1D11W1HXB5A80": {"value": "羽赫", "extendValue": "{\"identityType\":\"DINGTALK_ACCOUNT\",\"name\":\"羽赫\",\"id\":\"1\",\"jobNumber\":\"\",\"isSelf\":\"1\"}"}, "RecipientAccountField_1D11W1HXB5A81": {"value": "Mld12345", "extendValue": "{\"identityType\":\"PERSONAL_BANK_CARD\",\"instProvince\":\"浙江省\",\"name\":\"Mld12345\",\"instCity\":\"杭州市\",\"instBranchName\":\"招商银行股份有限公司杭州天目支行招商银行股份有限公司杭州天目支行招商银行股份有限公司杭州天目支行招商银行股份有限公司杭州天目支行招商银行股份有限公司杭州天目支行招商银行股份有限公司杭州天目支行\",\"id\":\"202503141776819002\",\"source\":\"receiptorAccount\",\"instCode\":\"CMB\",\"instName\":\"招商银行\",\"cardNo\":\"***************\"}"}, "RecipientAccountField_1D11W1HXB5A82": {"value": "杭州普回贸易有限公司", "extendValue": "{\"identityType\":\"CORP_BANK_CARD\",\"instProvince\":\"浙江省\",\"name\":\"杭州普回贸易有限公司\",\"instCity\":\"杭州市\",\"instBranchName\":\"浙江网商银行股份有限公司\",\"id\":\"202412101972996001\",\"source\":\"receiptorAccount\",\"instCode\":\"ANTBANK\",\"instName\":\"网商银行\",\"cardNo\":\"****************\",\"remark\":\"备注备注备注备注备注备注备注备注备注备注\"}"}, "RecipientAccountField_1D11W1HXB5A83": {"value": "杭州普回贸易有限公司", "extendValue": "{\"logonId\":\"<EMAIL>\",\"identityType\":\"ALIPAY_ACCOUNT\",\"name\":\"杭州普回贸易有限公司\",\"id\":\"202412021172323623\",\"source\":\"receiptorAccount\"}"}, "RelateField_1FRVHQWHVE680": {"value": "[\"羽赫提交的数据控件1明细\",\"羽赫提交的666-审批要手写签名\"]", "extendValue": "{\"list\":[{\"businessId\":\"202203151136000070819\",\"procInstId\":\"UZb3B6SOQfivCFEQcKNZGg03441647315367\"},{\"businessId\":\"202203131634000286941\",\"procInstId\":\"jMYDfF5PQzG9OrLWT6fFSA03441647160468\"}]}"}, "DDDateField_FEQOJVUT6G00": {"value": "2022-03-02"}, "MoneyField_1H36Q1FGG5400": {"value": "123456", "extendValue": "{\"upper\":\"壹拾贰万叁仟肆佰伍拾陆元整\",\"componentName\":\"MoneyField\"}"}, "IdCardField_9O1USYFYSBO0": {"value": "110101199003073159"}, "DDDateRangeField_1VOQS1567XZ40": {"value": "[\"2022-03-02\",\"2022-03-16\",15]"}, "procInstTitle": {"value": "羽赫提交的全控件测试"}, "InnerContactField_1OYU14MF4FDS0": {"value": "羽赫", "extendValue": "[{\"emplId\":\"*****************\",\"name\":\"羽赫\",\"avatar\":\"https://static.dingtalk.com/media/lADPBbCc1bX9vJvNDozNDow_3724_3724.jpg_250x250.jpg?bizType=avatar\"}]"}, "ExternalContactField_15EINWNN9YE80": {"value": "羽赫"}, "DDHolidayField_O1I9EDY7MBK0": {"value": "[\"2022-03-07\",\"2022-03-11\",5,\"day\",\"年假\",\"请假类型\"]", "extendValue": "{\"compressedValue\":\"1f8b0800000000000000bdd4cf4e83301800f077f9ced514564ae1b6b81897b88bce83313b34d0392283a52d9a65e1e0c9c7f1e81399f818b69b6364199210326e5fbf7e7ffa3bb08188a75191722d26792c207410c442f324bd4d9486f069037121b94ef26c9c8df8da5cb8c418d50e6ff24242c8b6a773c17521858270037cb54ad7d7325f4e93a5ed4b09a5ce006f3fb4cb4ef35a8e78e42f17a55ca97b11d9fe6abb82c8e261247365229337d1c93aa5b9d4b57bdbf8c4f47286205177c2be6fce532510bc0aa9cc3408c13c0f4a040baeaeec1ad58d55cab3710ce185017acbe5cbc888edfa7accdd6f601376e024c90a6d1908c325ea9530084813a13f705817c25a5d1be17e7acf8494327636429f79ac899039cced4258ab6b21aca6f74ce87b9e7b3642e6bb6e13618009ed4258ab6b21aca6f74cc88843cf4618788c3612060eee4478a86b21aca6f74c18b80cff473843c784de4942828f8eab2ef65f7b483c6489591c46c347306b156a31e5cf26fef9fcfa7eff80f21772a287acde060000\",\"extension\":\"{\\\"tag\\\":\\\"年假\\\"}\",\"unit\":\"DAY\",\"pushTag\":\"请假\",\"isNaturalDay\":false,\"detailList\":[{\"classInfo\":{\"hasClass\":false,\"sections\":[{\"endAcross\":0,\"startTime\":1646613000000,\"endTime\":1646645400000,\"startAcross\":0}]},\"workDate\":1646582400000,\"isRest\":false,\"workTimeMinutes\":480,\"approveInfo\":{\"fromAcross\":0,\"toAcross\":0,\"fromTime\":1646613000000,\"durationInDay\":1,\"durationInHour\":8,\"toTime\":1646645400000}},{\"classInfo\":{\"hasClass\":false,\"sections\":[{\"endAcross\":0,\"startTime\":1646699400000,\"endTime\":1646731800000,\"startAcross\":0}]},\"workDate\":1646668800000,\"isRest\":false,\"workTimeMinutes\":480,\"approveInfo\":{\"fromAcross\":0,\"toAcross\":0,\"fromTime\":1646699400000,\"durationInDay\":1,\"durationInHour\":8,\"toTime\":1646731800000}},{\"classInfo\":{\"hasClass\":false,\"sections\":[{\"endAcross\":0,\"startTime\":1646785800000,\"endTime\":1646818200000,\"startAcross\":0}]},\"workDate\":1646755200000,\"isRest\":false,\"workTimeMinutes\":480,\"approveInfo\":{\"fromAcross\":0,\"toAcross\":0,\"fromTime\":1646785800000,\"durationInDay\":1,\"durationInHour\":8,\"toTime\":1646818200000}},{\"classInfo\":{\"hasClass\":false,\"sections\":[{\"endAcross\":0,\"startTime\":1646872200000,\"endTime\":1646904600000,\"startAcross\":0}]},\"workDate\":1646841600000,\"isRest\":false,\"workTimeMinutes\":480,\"approveInfo\":{\"fromAcross\":0,\"toAcross\":0,\"fromTime\":1646872200000,\"durationInDay\":1,\"durationInHour\":8,\"toTime\":1646904600000}},{\"classInfo\":{\"hasClass\":false,\"sections\":[{\"endAcross\":0,\"startTime\":1646958600000,\"endTime\":1646991000000,\"startAcross\":0}]},\"workDate\":1646928000000,\"isRest\":false,\"workTimeMinutes\":480,\"approveInfo\":{\"fromAcross\":0,\"toAcross\":0,\"fromTime\":1646958600000,\"durationInDay\":1,\"durationInHour\":8,\"toTime\":1646991000000}}],\"durationInDay\":5,\"isModifiable\":true,\"durationInHour\":40}"}, "DDMultiSelectField_10M3VXPE2U9S0": {"value": "[\"选项1\",\"选项2\",\"选项3\"]", "extendValue": "[{\"label\":\"选项1\",\"key\":\"option_0\"},{\"label\":\"选项2\",\"key\":\"option_1\"},{\"label\":\"选项3\",\"key\":\"option_2\"}]"}, "DDAttachment_20ZZVGWIZYQO0": {"value": "[{\"uploadSize\":6686,\"spaceId\":1836509793,\"fileName\":\"繁体-撤销(1).png\",\"fileSize\":16077,\"fileType\":\"png\",\"fileId\":\"54270263021\"},{\"uploadSize\":10460,\"spaceId\":1836509793,\"fileName\":\"繁体-完成(2).png\",\"fileSize\":15141,\"fileType\":\"png\",\"fileId\":\"54270263022\"},{\"uploadSize\":11002,\"spaceId\":1836509793,\"fileName\":\"繁体-拒绝(1).png\",\"fileSize\":15049,\"fileType\":\"png\",\"fileId\":\"54270263023\"},{\"uploadSize\":10721,\"spaceId\":1836509793,\"fileName\":\"繁体-通过(2).png\",\"fileSize\":15767,\"fileType\":\"png\",\"fileId\":\"54270263024\"}]"}, "DDPhotoField_1QOR252PU11C0": {"value": "[\"https://static.dingtalk.com/media/lQLPDhs9ERgV0QHMwMzAsIMMKVkqKkzmAjS5mTMAYQA_192_192.png\",\"https://static.dingtalk.com/media/lQLPDhs9ERxB7ubMwMzAsIyPQs3zZZr2AjS5oAxAFAA_192_192.png\",\"https://static.dingtalk.com/media/lQLPDhs9ERxB85XMwMzAsHXPC8MuOaHyAjS5oIvAUwA_192_192.png\"]"}, "DDSelectField_BRBZ8OVCKLK0": {"value": "选项1", "extendValue": "{\"label\":\"选项1\",\"key\":\"option_0\"}"}, "DepartmentField_1MXXO4MR53K00": {"value": "B部门", "extendValue": "[{\"number\":0,\"name\":\"B部门\",\"id\":\"*********\"}]"}, "InvoiceField_1MSNC7RCEWRK0": {"value": "[\"昆山京东广臻贸易有限公司\"]", "extendValue": {"invoiceList": [{"amountWithoutTax": "13.82", "invoiceVerifyStatus": "CHECKING", "invoiceVerifyId": "1567834259551645696", "invoiceAmount": "14.65", "invoiceImgUrl": "https://down-cdn.dingtalk.com/ddmedia/iAELAqNqcGcDBgTNBR0FzQM_BtoAI4QBpCEz1I4CqmMMZygduoRSQWMDzwAAAYMczxapBM4AE8ZgBwAIAA.jpg", "source": "DINGPAN", "invoiceDate": "2022-06-16", "invoiceCode": "031002200111", "invoicePdfUrl": "https://down-cdn.dingtalk.com/ddmedia/iAEKAqNwZGYDBgTOXmw_fwXOA_hs8QbaACOEAaQhM-_WAqqjJeCWK7hTGpmmA88AAAGDHM8a2wTOABHCUAcACAA.pdf", "checkCode": "80332595114240396441", "payeeName": "浙江小望科技有限公司", "invoiceType": "PLAIN", "payerName": "浙江旋极所宜商务咨询有限公司", "invoiceId": "2022090819762612", "invoiceNo": "55426079", "taxAmount": "0.83", "payeeTaxNo": "91310120MA1HX39188", "payerTaxNo": "91330211MA2H47J163", "key": "2022090819762612", "expenseStatus": "WAIT_EXPENSE"}], "version": 2}}, "originatorDeptName": {"value": "测试审批团队", "extendValue": "测试审批团队"}, "TextField-K2AD4O5B": {"value": "这是一段测试文字"}, "pmc_business_id": {"value": "202203151521000280665"}, "leaverecordids": {"value": "[{\"corpId\":\"ding15019ffc2378260c35c2f4657eb6378f\",\"deleteFlag\":\"n\",\"endTime\":1647014399000,\"gmtCreate\":1647328889000,\"gmtModified\":1647328888000,\"id\":4246624,\"leaveCode\":\"246cee72-9871-489e-a713-c3f4d97c09cf\",\"leaveReason\":\"\",\"leaveViewUnit\":\"day\",\"recordId\":\"aa32dfec-5f4b-45b3-8aa6-4bfc72dc0b08\",\"recordNumPerDay\":500,\"recordNumPerHour\":4000,\"recordStatus\":\"init\",\"recordType\":\"leave\",\"remark\":\"{}\",\"startTime\":1646582400000,\"uid\":52736362,\"userId\":\"*****************\"}]"}, "procInstTitleEn": {}, "TextNote_1MTTZ2LVTM8W0": {"value": "请输入说明文字"}, "NumberField_QXE7VP4KC8W0": {"value": "3000"}, "SignatureField_1AJ71VJGODK00": {"value": "https://down-cdn.dingtalk.com/ddmedia/iAEKAqNwbmcDBgTNAlwFzMYG2gAjhAGkCxFu5gKqzSrHgwZnLJGRbQPPAAABfiRdcaAEzgASV68HAAgA.png", "extendValue": "{\"watermask\":\"仅限[全控件测试]使用\",\"sticker\":{\"time\":1647328816490,\"username\":\"羽赫\"},\"time\":1647328816490}"}, "AddressField_1SWHZ2VKVQ680": {"value": "北京,北京市,东城区", "extendValue": "{\"province\":{\"name\":\"北京\",\"id\":\"110000\"},\"city\":{\"name\":\"北京市\",\"id\":\"110100\"},\"district\":{\"name\":\"东城区\",\"id\":\"110101\"}}"}, "TableField_ZRMZXJW1M9S0": {"value": "[{\"rowValue\":[{\"key\":\"TextField_2XVR5JPRJIQ0\",\"label\":\"单行输入框\",\"value\":\"1\"},{\"key\":\"TextareaField_JMFEASN7Q3K0\",\"label\":\"多行输入框\",\"value\":\"2\"},{\"key\":\"NumberField_17RQPCDJIKG00\",\"label\":\"住宿费\",\"value\":\"3\"},{\"key\":\"NumberField_51U74SH7V1C0\",\"label\":\"餐饮费\",\"value\":\"4\"},{\"key\":\"DDSelectField_1W663CB4GI740\",\"label\":\"单选框\",\"value\":\"选项1\",\"extendValue\":{\"label\":\"选项1\",\"key\":\"option_0\"}},{\"key\":\"DDMultiSelectField_4R4EZCYHG8A0\",\"label\":\"多选框\",\"value\":[\"选项1\",\"选项2\"],\"extendValue\":[{\"label\":\"选项1\",\"key\":\"option_0\"},{\"label\":\"选项2\",\"key\":\"option_1\"}]},{\"key\":\"DDDateField_1GRJK8FFOZB40\",\"label\":\"日期\",\"value\":\"2022-03-31\"},{\"key\":\"DDDateRangeField_Z1BKSE48EI80\",\"label\":[\"开始时间\",\"结束时间\"],\"value\":[\"2022-03-24\",\"2022-03-31\",8]},{\"label\":\"图片\",\"value\":\"[\\\"https://static.dingtalk.com/media/lQLPDhs9EVAdFFjMwMzAsIY7M90E7v5mAjS59RFAYQA_192_192.png\\\"]\",\"key\":\"DDPhotoField_22TZIJOE56F40\"},{\"key\":\"TextNote_1GRJUV98UEW00\",\"label\":\"说明\",\"value\":\"请输入说明文字\"},{\"key\":\"MoneyField_1IO5FLCLW4680\",\"label\":\"金额（元）\",\"value\":\"13\"},{\"key\":\"DDAttachment_T25T3B47PV40\",\"label\":\"附件\",\"value\":[{\"fileId\":\"54270358535\",\"spaceId\":1836509793,\"fileName\":\"繁体-通过(1)(1).png\",\"fileType\":\"png\",\"uploadSize\":10721,\"fileSize\":15767}]},{\"key\":\"IdCardField_1E2DQ5WD9ZLS0\",\"label\":\"身份证\",\"value\":\"110101199003073159\"},{\"key\":\"PhoneField_1T7ECQ4UN8G00\",\"label\":\"电话\",\"value\":\"***********\",\"extendValue\":{\"areaNumber\":\"\",\"countryKey\":\"CN\",\"flag\":\"C\",\"countryCode\":\"+86\",\"flagPy\":\"Z\",\"countryNameZh\":\"中国\",\"countryName\":\"China\",\"countryNamePy\":\"ZHONGGUO\",\"mode\":\"phone\"}},{\"key\":\"InnerContactField_1L0YWEVW16ZK0\",\"label\":\"联系人\",\"value\":\"羽赫\",\"extendValue\":[{\"avatar\":\"https://static.dingtalk.com/media/lADPBbCc1bX9vJvNDozNDow_3724_3724.jpg_250x250.jpg?bizType=avatar\",\"name\":\"羽赫\",\"emplId\":\"*****************\"}]},{\"key\":\"DepartmentField_1FN2AM21VRB40\",\"label\":\"部门\",\"value\":\"A部门\",\"extendValue\":[{\"id\":\"120417193\",\"name\":\"A部门\",\"number\":0}]},{\"key\":\"RelateField_TUJV4CTF5CG0\",\"label\":\"关联审批单\",\"value\":[\"羽赫提交的666-审批要手写签名\",\"羽赫提交的666-审批要手写签名\"],\"extendValue\":{\"list\":[{\"procInstId\":\"Vg4w3zcnT8yhKJhWkVcIxQ03441647160343\",\"businessId\":\"202203131632000236741\"},{\"procInstId\":\"fPVUFohLSJWFCcWajlmGDA03441646969918\",\"businessId\":\"202203111138000386122\"}]}},{\"key\":\"AddressField_16UY0SO0F1LS0\",\"label\":\"省市区\",\"value\":\"天津,天津市,河西区\",\"extendValue\":{\"province\":{\"id\":\"120000\",\"name\":\"天津\"},\"city\":{\"id\":\"120100\",\"name\":\"天津市\"},\"district\":{\"id\":\"120103\",\"name\":\"河西区\"}}},{\"key\":\"StarRatingField_TID0DHHAG4G0\",\"label\":\"评分\",\"value\":3},{\"key\":\"CalculateField_MHPAS7YUUS00\",\"label\":\"计算公式\",\"value\":\"7\"}],\"rowNumber\":\"TableField_ZRMZXJW1M9S0_12VPGNN9BE800\"}]", "extendValue": "{\"statValue\":[],\"componentName\":\"TableField\"}"}, "TextareaField_3954P8M5MIY0": {"value": "这是一段测试文字2"}, "originatorDeptId": {"value": "-1"}, "CalculateField_SRVOJRIGQM80": {"value": "33", "extendValue": "{\"upper\":\"叁拾叁元整\",\"componentName\":\"MoneyField\"}"}, "PhoneField_O1HV7C9H3MO0": {"value": "***********", "extendValue": "{\"mode\":\"phone\",\"countryKey\":\"CN\",\"flag\":\"C\",\"countryCode\":\"+86\",\"areaNumber\":\"\",\"flagPy\":\"Z\",\"countryNameZh\":\"中国\",\"countryName\":\"China\",\"countryNamePy\":\"ZHONGGUO\"}"}, "processCode": {"value": "PROC-70694D23-1975-4C47-86E5-3719D6C1A8D1"}, "procInstId": {"value": "202203151521000280665"}, "RelateField_1FRVHQWHVE681": {"value": "[\"承漪的档案\"]", "extendValue": "{\"quote\": \"1\", \"source\": \"上下游\", \"sourceId\": \"123\", \"sourceName\": \"成一测试企业\",  \"canChange\": false,   \"list\": [     {       \"formCode\": \"\",       \"instanceId\": \"\",       \"rowValue\": [         {           \"extendValue\": \"{\\\"uid\\\":571825328,\\\"nickName\\\":\\\"周凯\\\"}\",           \"label\": \"用户授权\",           \"key\": \"authorization\",           \"bizAlias\": \"authorization\",           \"value\": \"周凯\",           \"componentType\": \"AuthorizationField\"         }       ],       \"bizType\": \"\"     }   ] }"}, "TableField_WD2TRICVFZ40": {"value": "[{\"rowValue\":[{\"key\":\"DDDateRangeField_IA50KN237LK0\",\"label\":[\"开始时间\",\"结束时间\"],\"value\":[\"2022-12-14\",null,\"\"]},{\"key\":\"TextField_RISI4JSRXYO0\",\"label\":\"单行输入框\",\"value\":\"123\"},{\"key\":\"NumberField_1MLZZ2CBQUF40\",\"label\":\"数字输入框\",\"value\":\"14123123\"},{\"key\":\"DDSelectField_1UKDJRNZV60W0\",\"label\":\"单选框\",\"value\":\"选项2\",\"extendValue\":{\"label\":\"选项2\",\"key\":\"option_1\"}},{\"key\":\"TextareaField_1644VMQGMR9C0\",\"label\":\"多行输入框\",\"value\":\"asdasd\"}],\"rowNumber\":\"TableField_WD2TRICVFZ40_228IKN71PGM80\"}]", "extendValue": "{\"statValue\":[],\"componentName\":\"TableField\"}"}}