{"DDSelectField_1K1E6HV08SF7K": {"value": "选项1", "extendValue": {"label": "选项1", "key": "option_0"}, "mask": false}, "NumberField_HYOCQQBXIARK": {"value": 11, "mask": false}, "SignatureField_Z4R8ZIOAK8W0": {"value": "https://down.dingtalk.com/ddmedia/iQELAqNwbmcDAgTMwQXMuwbaACGEAaQhCAHRAqoudud2OXE-TJsbA88AAAGU-Wn6oQTNhgQHAAgACadhcHByb3Zl.png", "extendValue": {"watermask": "仅限[allFields测试]使用", "sticker": {"time": 1739428996848, "username": "李康"}, "time": 1739428996848}, "mask": false}, "bpms_instVariable_inst_status": {"value": "RUNNING", "mask": false}, "DDPhotoField_5FVWS6YRNYM8": {"value": ["http://i01.lw.aliimg.com/media/lQLPD33SvxjUnonM2MzYsLrcjDemSXHSB5EjDQouXgA_216_216.png"], "mask": false}, "procInstTitle": {"value": "李康提交的allFields测试", "mask": false}, "PhoneField_1ZZT3JDUMOA9S": {"value": 15527038105, "extendValue": {"mode": "phone", "countryKey": "CN", "flag": "C", "countryCode": "+86", "areaNumber": "", "flagPy": "Z", "countryNameZh": "中国", "countryName": "China", "countryNamePy": "ZHONGGUO"}, "mask": false}, "SeqNumberField_1XA8AO8F8XKW0": {"value": 2025021300000002, "mask": false}, "DDMultiSelectField_23WS4OH20MTQ8": {"value": ["选项2", "选项3"], "extendValue": [{"label": "选项2", "key": "option_1"}, {"label": "选项3", "key": "option_2"}], "mask": false}, "procInstTitleEn": {"value": "李康提交的allFields测试", "mask": false}, "IdCardField_22CQ3YUUKTNGG": {"value": ******************, "mask": false}, "DDAttachment_F37URH99B5KW": {"value": [{"spaceId": "4782723059", "fileName": "反馈图标@3x.png", "thumbnail": {"authCode": "", "rotation": 0, "authMediaId": "$iQELAqNqcGcDAQTM2AXM2AbaACGEAaQhCAHRAqrERqgTvnsrEgSrA88AAAGU_hNmnATNmxcHAAgACapkaW5ncGFuUGlj", "width": 216, "mediaId": "", "height": 216}, "fileSize": "4098", "fileType": "png", "fileId": "169113263148"}], "mask": false}, "AddressField_SIYVYACJ6BCW": {"value": "北京,北京市,东城区", "extendValue": {"province": {"name": "北京", "id": 110000}, "city": {"name": "北京市", "id": 110100}, "district": {"name": "东城区", "id": 110101}}, "mask": false}, "CalculateField_VD6Z9Q0W524G": {"value": 122, "extendValue": {"upper": "壹佰贰拾贰元整", "componentName": "MoneyField"}, "mask": false}, "bpms_instVariable_inst_creator": {"value": 3101202705844073, "mask": false}, "originatorDeptName": {"value": "樱桃最可爱", "extendValue": "樱桃最可爱", "mask": false}, "DDDateRangeField_15ZDNEY5KNHFK": {"value": ["2025-02-13", "2025-02-14", ""], "mask": false}, "TextField-K2AD4O5B": {"value": "测试1111111", "mask": false}, "OcrIdCardField_1RWVKWHRWN400": {"value": [{"bizAlias": "name", "label": "姓名", "value": "1111", "key": "TextField_COEWHK0NW3CW"}, {"bizAlias": "idCardNo", "label": "身份证号码", "value": "******************", "key": "IdCardField_1KIL4I8CJPPFK"}, {"bizAlias": "gender", "label": "性别", "extendValue": {"label": "男", "key": "0"}, "value": "男", "key": "DDSelectField_BWM7NQK0QC5C"}, {"bizAlias": "nation", "label": "民族", "value": "汉", "key": "TextField_1OD8K6ZEAZA4G"}, {"bizAlias": "birthdate", "label": "出生日期", "value": "2025-02-12", "key": "DDDateField_15J71EFEST1C0"}, {"bizAlias": "address", "label": "住址", "value": "收拾收拾", "key": "TextField_E8L22RK7MIV4"}, {"bizAlias": "issuingAuthority", "label": "签发机关", "value": "事实上", "key": "TextField_8PQTMO4T6OW0"}, {"bizAlias": "expirationStartDate", "label": "有效期限起始时间", "value": "2025-02-11", "key": "DDDateField_1QL5VJE4Y5YIO"}, {"bizAlias": "expirationEndDate", "label": "有效期限结束时间", "value": "2025-02-28", "key": "DDDateField_A6GM99RXUTXC"}], "extendValue": ["https://i01.lw.aliimg.com/media/lQLPD4UTBmpR-UnNBLDNDhCwQI7alUlWTJYHkSNjvd8mAA_3600_1200.png", "https://i01.lw.aliimg.com/media/lQLPM34qndQQG8ljY7DRmpIseg_gpweRI2nqWfMA_99_99.png"], "mask": false}, "pmc_business_id": {"value": 202502131452000400000, "mask": false}, "InnerContactField_VYM8XR97GV0G": {"value": "文疏", "extendValue": [{"emplId": "68674200835816", "itemId": "68674200835816", "name": "文疏", "avatar": "http://i01.lw.aliimg.com/media/lADPD4BhzYX9B4zNAgDNAgA_512_512.jpg"}], "mask": false}, "TableField_1EMRKW5WU55A8": {"value": [{"rowNumber": "TableField_1EMRKW5WU55A8_EMV4Z6HEZ6RK", "rowValue": [{"key": "TextField_DNKUQYMKUI2O", "label": "单行输入框", "mask": false, "value": "1111"}]}], "extendValue": {"statValue": [], "componentName": "TableField"}, "mask": false}, "DDDateRangeField_1ZCT44D5E4WLC": {"value": ["2025-02-14", "2025-02-15", 2], "mask": false}, "DDDateField_QPAO274JMVI8": {"value": "2025-02-13", "mask": false}, "MoneyField_1Q1G8UT7EESXS": {"value": 11, "extendValue": {"upper": "壹拾壹元整", "componentName": "MoneyField"}, "mask": false}, "RecipientAccountField_3EMJVRO5QSSG": {"value": "李康", "extendValue": {"identityType": "DINGTALK_ACCOUNT", "name": "李康", "id": 1, "source": "", "jobNumber": "", "isSelf": 1}, "mask": false}, "StarRatingField_HTRPJK3ULXC0": {"value": 5, "mask": false}, "TextNote_KRJSZ5C5HW5C": {"value": "请输入说明文字", "mask": false}, "TextareaField_1A690U9UCI29S": {"value": "不会被杀被忽视你手机啊不是叫你爸就说吧十八岁结婚吧喝酒撒上", "mask": false}, "originatorDeptId": {"value": -1, "mask": false}, "processCode": {"value": "PROC-DA857EFB-C57C-4A69-B05E-CB418701E6EB", "mask": false}, "procInstId": {"value": 202502131452000400000, "mask": false}, "TimeAndLocationField_1IKE57W9Z3LS0": {"value": ["2025-03-04 16:30:28", 120.021195, 30.281506, "浙江省杭州市余杭区文一西路960-1号阿里巴巴西溪园区C区阿里巴巴西溪C区(C6楼)", 50], "extendValue": "当前时间:2025-03-04 16:30:28\n当前地点:浙江省杭州市余杭区文一西路960-1号阿里巴巴西溪园区C区阿里巴巴西溪C区(C6楼)", "mask": false}, "origin_department": {"value": "樱桃最可爱", "extendValue": -1, "key": "origin_department"}, "RelateField_140UD1G0E3YM8": {"value": ["长清（主用钉）提交的allFields", "长清（主用钉）提交的allFields"], "extendValue": {"list": [{"businessId": "202503031601000257005", "procInstId": "lYh-GTBFTPaem00NQa-31g09641740988884"}, {"businessId": "202503031524000545261", "procInstId": "O6khy9gyQJ-2_x49qQOLYw09641740986693"}]}, "mask": false}, "FormRelateField_MC16NUEC6QKG": {"value": ["长清（主用钉）提交的测试1111"], "extendValue": {"list": [{"formCode": "PROC-3560876A-A313-44D2-80BE-F9601F1F8C36", "instanceId": "g7lPFzv8SoS_09lfYhSEKg09641740973085", "rowValue": [{"key": "TextField-J78F056R", "label": "单行输入框", "mask": false, "value": "111"}, {"extendValue": "{\"upper\":\"壹拾壹元整\",\"componentName\":\"MoneyField\"}", "key": "MoneyField-J78F0571", "label": "金额（元）大写", "mask": false, "value": "11"}, {"key": "TextareaField-J78F056S", "label": "多行输入框", "mask": false, "value": "11"}, {"key": "NumberField-J78F057N", "label": "数字输入框", "mask": false, "value": "11"}]}], "quote": 1}, "mask": false}}