{"DDDateField_NBGOPZZ3IE80": {"value": "2022-01-24"}, "SeqNumberField_1C1FRHLDQV28": {"value": "2022012400000004"}, "TableField_1NENDTT9QMU80": {"value": "[{\"rowValue\":[{\"label\":\"物料\",\"value\":[\"学友测试物料111\"],\"key\":\"FormRelateField_Q5RTXBOO3CG0\",\"extendValue\":{\"quote\":0,\"list\":[{\"formCode\":\"PROC-12B90453-6E33-486D-B776-CE2328BB9705\",\"bizType\":\"dingtalk.palmmanufacture.material\",\"instanceId\":\"fdd26d89-185e-4546-a15e-04b9ef6e832c\",\"rowValue\":[{\"label\":\"物料编号\",\"key\":\"SeqNumberField_NSBF6SAX2SG0\",\"bizAlias\":\"material_no\",\"value\":\"00000119\",\"componentType\":\"SeqNumberField\"},{\"label\":\"名称\",\"key\":\"TextField_K8BWAVZ9V0W0\",\"bizAlias\":\"material_name\",\"value\":\"学友测试物料111\",\"componentType\":\"TextField\"},{\"extendValue\":\"{\\\"label\\\":\\\"千克\\\",\\\"key\\\":\\\"option_1\\\"}\",\"label\":\"计量单位\",\"key\":\"DDSelectField_4WMFE6MF7EE0\",\"bizAlias\":\"unit\",\"value\":\"千克\",\"componentType\":\"DDSelectField\"}]}]},\"bizAlias\":\"goods\"},{\"label\":\"库位\",\"value\":[\"食品001\"],\"key\":\"FormRelateField_KPPQFXRBXOW0\",\"extendValue\":{\"quote\":0,\"list\":[{\"formCode\":\"PROC-2592CC65-35F8-4838-BF2A-83245091BBCC\",\"bizType\":\"dingtalk.wms.storage\",\"instanceId\":\"r1bsVWJ_TCKoEClvr-Ox0w07411642559496\",\"rowValue\":[{\"label\":\"库位编号\",\"key\":\"SeqNumberField_1PLPHI3W23WG0\",\"bizAlias\":\"storage_no\",\"value\":\"0047\"},{\"label\":\"库位\",\"key\":\"TextField_1TXF1IBLYNA80\",\"bizAlias\":\"storage_name\",\"value\":\"食品001\"}]}]},\"bizAlias\":\"storage\"},{\"label\":\"入库数量\",\"value\":\"12\",\"key\":\"NumberField_1R6RG1368D280\",\"bizAlias\":\"actual_receive_count\"}],\"rowNumber\":\"TableField_1NENDTT9QMU80_KJ86EO28LLO0\"}]", "extendValue": "{\"statValue\":[],\"componentName\":\"TableField\"}"}, "pmc_business_id": {"value": "202201241143000313656"}, "procInstTitle": {"value": "郑志豪提交的生产入库单"}, "FormRelateField_22H58689H3TS0": {"value": "[\"a-aa 2022-01-19\"]", "extendValue": "{\"quote\":0,\"list\":[{\"bizType\":\"dingtalk.palmmanufacture.task_order\",\"instanceId\":\"9J8YV2JRTV2S7Fwkt5Ui9A07411642573199\",\"rowValue\":[{\"componentType\":\"SeqN<PERSON>berField\",\"bizAlias\":\"task_order_no\",\"label\":\"生产单号\",\"value\":\"202201190002\",\"key\":\"SeqNumberField_1TUA06RKDCIO0\"}],\"formCode\":\"PROC-F2FB9F1B-B4E8-4488-866D-C5CCDC5E880F\"}]}"}, "originatorDeptName": {"value": "生产部门", "extendValue": "生产部门"}, "processCode": {"value": "PROC-BD29A60E-A083-433F-AD43-C5E4E0EC313D"}, "procInstTitleEn": {}, "originatorDeptId": {"value": "560239785"}, "FormRelateField_1ER1A50TAYKG0": {"value": "[\"郑志豪提交的销售订单\"]", "extendValue": "{\"quote\":1,\"list\":[{\"bizType\":\"\",\"instanceId\":\"7BYg82RhRj-5JqIeTltn7g07411640743129\",\"rowValue\":[{\"value\":\"2021122900000028\",\"key\":\"SeqNumberField_1Y0WILAE4PB40\"}],\"formCode\":\"PROC-B9D15BB6-E949-41E8-B9A7-CB7C00A5FE19\"}]}"}, "procInstId": {"value": "202201241143000313656"}}