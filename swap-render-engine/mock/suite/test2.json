[{"componentName": "DDDateField", "props": {"unit": "天", "rely": {"formula": "TODAY()", "type": "formula", "fields": [], "version": 2}, "defaultValue": "", "format": "yyyy-MM-dd", "bizAlias": "biz_time", "label": "入库时间", "placeholder": "请选择", "id": "DDDateField_NBGOPZZ3IE80", "fields": [], "dataSource": {}, "required": true}}, {"children": [{"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "goods", "label": "物料", "id": "FormRelateField_Q5RTXBOO3CG0", "title": "", "hiddenDetail": true, "fields": [{"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}], "bizAlias": "material_no", "label": "物料编号", "id": "SeqNumberField_NSBF6SAX2SG0"}}, {"componentName": "TextField", "props": {"bizAlias": "material_name", "label": "名称", "placeholder": "请输入", "id": "TextField_K8BWAVZ9V0W0", "required": true, "ratio": 50}}, {"componentName": "DDSelectField", "props": {"rely": {}, "options": [{"value": "个", "key": "option_0"}, {"value": "千克", "key": "option_1"}, {"value": "箱", "key": "option_2"}, {"extension": {"image": ""}, "value": "盒", "key": "option_MKY6CYLIRVK0"}], "bizAlias": "unit", "label": "计量单位", "placeholder": "请选择", "id": "DDSelectField_4WMFE6MF7EE0", "fields": [], "dataSource": {}, "required": false, "spread": false, "ratio": 50, "behaviorLinkage": []}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "dingtalk.palmmanufacture.material", "formCode": "PROC-12B90453-6E33-486D-B776-CE2328BB9705", "appType": 1}}, "required": true, "multi": 0}}, {"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "storage", "label": "库位", "id": "FormRelateField_KPPQFXRBXOW0", "hiddenDetail": true, "title": "", "fields": [{"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}], "bizAlias": "storage_no", "label": "编号", "id": "SeqNumberField_1PLPHI3W23WG0"}}, {"componentName": "TextField", "props": {"bizAlias": "storage_name", "label": "库位号", "placeholder": "请输入", "id": "TextField_1TXF1IBLYNA80", "required": true, "ratio": 50}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "dingtalk.wms.storage", "formCode": "PROC-2592CC65-35F8-4838-BF2A-83245091BBCC", "appType": 1}}, "required": true, "multi": 0}}, {"componentName": "NumberField", "props": {"bizAlias": "actual_receive_count", "label": "入库数量", "placeholder": "请输入数字", "id": "NumberField_1R6RG1368D280", "required": true, "ratio": 50}}], "componentName": "TableField", "props": {"tableViewMode": "table", "bizAlias": "stock_detail", "label": "入库详情", "id": "TableField_1NENDTT9QMU80", "actionName": "添加"}}]