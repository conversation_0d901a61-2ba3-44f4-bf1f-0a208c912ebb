[{"componentName": "CarouselView", "name": "Banner轮播", "icon": "https://gw.alicdn.com/tfs/TB1G9tTaBr0gK0jSZFnXXbRRXXa-144-144.png", "category": "common", "grid": 1, "props": {"id": "CarouselView_jx4kv3w", "version": "0.0.1", "label": "Banner轮播", "borderRadius": true, "autoplay": true, "source": 0, "autoplayInterval": 3, "imageUploadType": 0, "linkType": 0, "bizAlias": "carouselView", "env": "dev", "httpFetchParams": {"type": "HSF", "version": "1", "apiKey": "getCarouselView", "params": ""}, "lwpFetchParams": {"type": "HSF", "version": "1", "apiKey": "getCarouselView", "params": ""}, "defaultImage": {"source": 0, "src": "", "customSrc": ""}, "dataSource": [{"image": "https://gw.alicdn.com/tfs/TB1kGnhaUT1gK0jSZFrXXcNCXXa-1029-414.png", "link": "https://www.dingtalk.com/"}, {"image": "https://gw.alicdn.com/tfs/TB1kGnhaUT1gK0jSZFrXXcNCXXa-1029-414.png", "link": "https://www.dingtalk.com/"}]}}, {"componentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "公告", "icon": "https://gw.alicdn.com/tfs/TB1JfpTarj1gK0jSZFuXXcrHpXa-144-144.png", "category": "common", "grid": 1, "props": {"id": "AnnonceView_jx4kv3w1", "version": "0.0.1", "logo": {"source": 1, "src": "https://gw.alicdn.com/tfs/TB1JfpTarj1gK0jSZFuXXcrHpXa-144-144.png", "customSrc": "https://gw.alicdn.com/tfs/TB1G9tTaBr0gK0jSZFnXXbRRXXa-144-144.png"}, "label": "公告", "num": 2, "autoplay": true, "autoplayInterval": 3, "bizAlias": "annonceView", "env": "dev", "source": 0, "httpFetchParams": {"type": "HSF", "version": "1", "apiKey": "getAnnonceView", "params": ""}, "lwpFetchParams": {"type": "HSF", "version": "1", "apiKey": "getAnnonceView", "params": ""}, "viewMoreLink": "https://www.dingtalk.com/", "dataSource": [{"title": "钉钉,数字化新工作方式,让工作更简单!", "link": "https://www.dingtalk.com/"}, {"title": "钉钉,数字化新工作方式,让工作更简单!", "link": "https://www.dingtalk.com/"}, {"title": "钉钉,数字化新工作方式,让工作更简单!", "link": "https://www.dingtalk.com/"}, {"title": "钉钉,数字化新工作方式,让工作更简单!", "link": "https://www.dingtalk.com/"}, {"title": "钉钉,数字化新工作方式,让工作更简单!", "link": "https://www.dingtalk.com/"}, {"title": "钉钉,数字化新工作方式,让工作更简单!", "link": "https://www.dingtalk.com/"}]}}, {"componentName": "AppTabView", "name": "应用Tab", "icon": "https://gw.alicdn.com/tfs/TB1gmlSarr1gK0jSZFDXXb9yVXa-144-144.png", "category": "common", "grid": 1, "props": {"id": "AppTabView_22jszkw11", "version": "0.0.1", "label": "应用Tab", "showLabel": true, "columnNum": 4, "num": 3, "updateLogo": true, "bizAlias": "appTabView", "env": "dev", "httpFetchParams": {"apiKey": "designAppListView", "version": 1, "type": "HSF", "params": ""}, "lwpFetchParams": {"apiKey": "getAppListView", "version": 1, "type": "HSF", "params": ""}, "dataSource": [{"title": "教育", "items": [{"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}]}, {"title": "培训", "items": [{"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}]}, {"title": "考勤", "items": [{"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 0, "appId": 123}]}]}}, {"componentName": "ImageGridView", "name": "宫格", "icon": "https://gw.alicdn.com/tfs/TB1_JxTaq61gK0jSZFlXXXDKFXa-144-144.png", "category": "common", "grid": 1, "props": {"id": "ImageGridView_jszkw11", "label": "宫格", "showLabel": true, "layout": -2, "linkType": 0, "bizAlias": "imageGridView", "height": 150, "env": "dev", "httpFetchParams": {"apiKey": "designAppListView", "version": 1, "type": "HSF", "params": ""}, "lwpFetchParams": {"apiKey": "getAppListView", "version": 1, "type": "HSF", "params": ""}, "dataSource": [{"image": "", "icon": "", "appType": 0, "appId": 123}, {"image": "", "icon": "", "appType": 0, "appId": 123}, {"image": "", "icon": "", "appType": 0, "appId": 123}, {"image": "", "icon": "", "appType": 0, "appId": 123}, {"image": "", "icon": "", "appType": 0, "appId": 123}]}}, {"componentName": "MsgCardView", "name": "资讯卡片", "icon": "https://gw.alicdn.com/tfs/TB1JDVRauL2gK0jSZFmXXc7iXXa-144-144.png", "category": "common", "grid": 1, "props": {"id": "MsgCardView_jszkw11", "version": "0.0.1", "label": "资讯卡片", "showLabel": true, "num": 3, "bizAlias": "msgCardView", "env": "dev", "source": 1, "httpFetchParams": {"type": "YGW", "version": "1", "apiKey": ""}, "lwpFetchParams": {"type": "YGW", "version": "1", "apiKey": ""}, "dataSource": [{"title": "从打卡器到造血机,钉钉与企业的双螺旋进化", "link": "www.dingtalk.com", "source": "小钉", "image": "", "dateTime": 1562660918545}, {"title": "从打卡器到造血机,钉钉与企业的双螺旋进化", "link": "www.dingtalk.com", "source": "小钉", "image": "", "dateTime": 1562660918545}, {"title": "从打卡器到造血机,钉钉与企业的双螺旋进化", "link": "www.dingtalk.com", "source": "小钉", "image": "", "dateTime": 1562660918545}, {"title": "从打卡器到造血机,钉钉与企业的双螺旋进化", "link": "www.dingtalk.com", "source": "小钉", "image": "", "dateTime": 1562660918545}, {"title": "从打卡器到造血机,钉钉与企业的双螺旋进化", "link": "www.dingtalk.com", "source": "小钉", "image": "", "dateTime": 1562660918545}]}}, {"componentName": "AppListView", "name": "应用列表", "icon": "https://gw.alicdn.com/tfs/TB1gmlSarr1gK0jSZFDXXb9yVXa-144-144.png", "category": "common", "grid": 1, "props": {"id": "AppListView_jszkw11", "version": "0.0.1", "label": "应用列表", "showLabel": true, "columnNum": 4, "bizAlias": "appListView", "carouselMaxRow": 2, "allowIconChange": true, "env": "dev", "httpFetchParams": {"apiKey": "designAppListView", "version": 1, "type": "HSF", "params": ""}, "lwpFetchParams": {"apiKey": "getAppListView", "version": 1, "type": "HSF", "params": ""}, "updateLogo": true, "dataSource": [{"icon": "", "image": "", "name": "应用", "appType": 1, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 1, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 1, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 1, "appId": 123}, {"icon": "", "image": "", "name": "应用", "appType": 1, "appId": 123}]}}, {"componentName": "Customer<PERSON><PERSON><PERSON>", "name": "智能客服", "icon": "https://gw.alicdn.com/tfs/TB1TF8SaEz1gK0jSZLeXXb9kVXa-144-144.png", "category": "common", "grid": 1, "props": {"id": "CustomerView_jx4kv3w1", "version": "0.0.1", "layout": 0, "label": "智能客服", "logo": {"source": 1, "src": "https://gw.alicdn.com/tfs/TB1TF8SaEz1gK0jSZLeXXb9kVXa-144-144.png", "customSrc": "https://gw.alicdn.com/tfs/TB1G9tTaBr0gK0jSZFnXXbRRXXa-144-144.png"}, "link": "https://page.dingtalk.com/wow/dingtalk/act/smartrobotsetting?corpId=$CORPID$#!/"}}, {"componentName": "<PERSON><PERSON><PERSON>ie<PERSON>", "name": "待办", "icon": "https://gw.alicdn.com/tfs/TB1JfpTarj1gK0jSZFuXXcrHpXa-144-144.png", "category": "common", "grid": 1, "props": {"id": "TodoView_jx4kv3w1", "version": "0.0.1", "label": "待办", "showLabel": true, "bizAlias": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "env": "dev"}}]