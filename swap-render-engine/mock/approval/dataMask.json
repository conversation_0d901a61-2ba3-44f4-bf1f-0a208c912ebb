[{"componentName": "TextField", "props": {"id": "TextField-K2AD4O5B", "label": "单行输入框"}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- 普通控件start ----------------------------------", "style": {"color": "red"}}}, {"componentName": "RecipientAccountField", "props": {"bizAlias": "", "label": "收款账户", "placeholder": "请选择", "id": "RecipientAccountField_4XP80U9UPDC0", "required": false, "mask": true}}, {"componentName": "IdCardField", "props": {"bizAlias": "", "label": "身份证", "placeholder": "请输入", "id": "IdCardField_1S795HKSCYV40", "required": false, "mask": true}}, {"componentName": "TimeAndLocationField", "props": {"bizAlias": "", "label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_1SDFDVVNCX340", "required": false, "mask": true}}, {"componentName": "PhoneField", "props": {"mode": "phone", "bizAlias": "", "label": "电话", "placeholder": "请输入", "id": "PhoneField_1CCRC0929GSG0", "required": false, "mask": true}}, {"componentName": "AddressField", "props": {"needDetail": false, "bizAlias": "", "label": "省市区", "id": "AddressField_H0Y6Z2DR3200", "required": false, "mask": true}}, {"componentName": "AddressField", "props": {"needDetail": true, "bizAlias": "", "label": "省市区街道", "id": "AddressField_19ETTM5PKPS00", "required": false, "mask": true}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- 普通控件end ----------------------------------", "style": {"color": "red"}}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- 明细/表格start ----------------------------------", "style": {"color": "red"}}}, {"children": [{"componentName": "IdCardField", "props": {"bizAlias": "", "label": "身份证", "placeholder": "请输入", "id": "IdCardField_6YU1V8HHKWK0", "required": false, "mask": true}}, {"componentName": "PhoneField", "props": {"mode": "phone", "bizAlias": "", "label": "电话", "placeholder": "请输入", "id": "PhoneField_CBSKQU6RZ8W", "required": false, "mask": true}}, {"componentName": "AddressField", "props": {"needDetail": true, "bizAlias": "", "label": "省市区街道", "id": "AddressField_1E4PPTOI3D5S0", "required": false, "mask": true}}, {"componentName": "TimeAndLocationField", "props": {"bizAlias": "", "label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_3EVLZ1GQXHC0", "required": false, "mask": true}}], "componentName": "TableField", "props": {"tableViewMode": "table", "label": "表格", "id": "TableField_4TWJZ2ATRP40", "actionName": "添加"}}, {"children": [{"componentName": "IdCardField", "props": {"bizAlias": "", "label": "身份证", "placeholder": "请输入", "id": "IdCardField_GKM600RSUFS0", "required": false, "mask": true}}, {"componentName": "PhoneField", "props": {"mode": "phone", "bizAlias": "", "label": "电话", "placeholder": "请输入", "id": "PhoneField_EF97L6JL8M80", "required": false, "mask": true}}, {"componentName": "AddressField", "props": {"needDetail": true, "bizAlias": "", "label": "省市区街道", "id": "AddressField_RFRZV5411TC0", "required": false, "mask": true}}, {"componentName": "TimeAndLocationField", "props": {"bizAlias": "", "label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_JZMDZAL0CM80", "required": false, "mask": true}}], "componentName": "TableField", "props": {"tableViewMode": "list", "bizAlias": "", "label": "表格", "id": "TableField_1VLW2XL733400", "actionName": "添加"}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- 明细/表格end ----------------------------------", "style": {"color": "red"}}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- 身份证Ocr start ----------------------------------", "style": {"color": "red"}}}, {"children": [{"componentName": "TextField", "props": {"bizAlias": "name", "label": "姓名", "placeholder": "请输入", "id": "TextField_1WAJTYL8VYKG0", "required": true}}, {"componentName": "IdCardField", "props": {"bizAlias": "idCardNo", "label": "身份证号码", "placeholder": "请输入", "id": "IdCardField_KW0W2D3AOG00", "required": true}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "男", "key": "0"}, {"value": "女", "key": "1"}], "bizAlias": "gender", "label": "性别", "placeholder": "请选择", "id": "DDSelectField_11SMLWR6O79C0", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "nation", "label": "民族", "placeholder": "请输入", "id": "TextField_1OH2SWTV4RCW0", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "birthdate", "label": "出生日期", "placeholder": "请选择", "id": "DDDateField_RVO2KK4RMTS0", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "address", "placeholder": "请输入", "label": "住址", "id": "TextField_23I6FGH5XG800", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "issuingAuthority", "label": "签发机关", "placeholder": "请输入", "id": "TextField_1H3F3CBSDEE80", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "expirationStartDate", "label": "有效期限起始时间", "placeholder": "请选择", "id": "DDDateField_9X27299CSOW0", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "expirationEndDate", "label": "有效期限结束时间", "placeholder": "请选择", "id": "DDDateField_CLYRXIADVQG0", "required": true}}], "componentName": "OcrIdCardField", "props": {"options": ["name", "idCardNo", "gender", "nation", "birthdate", "address", "issuingAuthority", "expirationStartDate", "expirationEndDate"], "ocrType": "idcard", "bizAlias": "", "label": "身份证识别", "placeholder": "识别出的信息会自动填充到表单", "id": "OcrIdCardField_17L3C65FRRLS0", "type": "ocr", "required": true, "mask": true}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- 身份证Ocr end ----------------------------------", "style": {"color": "red"}}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 拷贝 start ----------------------------------", "style": {"color": "red"}}}, {"componentName": "FormRelateField", "props": {"displayExtract": true, "securityMode": 0, "bizAlias": "", "label": "省市区控件", "title": "", "required": false, "multi": 0, "quote": 0, "extract": true, "id": "FormRelateField_FZVEYQ5JA3K0", "fields": [{"componentName": "AddressField", "props": {"_extractId": "AddressField_15U88PRN5AAO0", "needDetail": true, "bizAlias": "", "label": "省市区", "id": "AddressField_15U88PRN5AAO0", "required": false, "mask": true, "_oriId": "AddressField_1H0XALI6MVWG0"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-7617C171-4495-4E51-B8EB-CB2670E02472", "appType": 0}}, "procType": ""}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 拷贝 start ----------------------------------", "style": {"color": "red"}}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 引用含明细 start ----------------------------------", "style": {"color": "red"}}}, {"componentName": "FormRelateField", "props": {"displayExtract": true, "quote": 1, "extract": false, "securityMode": 0, "label": "数据脱敏", "id": "FormRelateField_BA964WZ8V1MO", "title": "", "fields": [{"componentName": "PhoneField", "props": {"mode": "phone", "_extractId": "PhoneField_54ZJ45LFOLMO", "bizAlias": "", "label": "手机号", "placeholder": "请输入", "id": "PhoneField_DRUCX5NGWBK0", "required": false, "mask": true, "_oriId": "PhoneField_DRUCX5NGWBK0"}}, {"componentName": "PhoneField", "props": {"mode": "tel", "_extractId": "PhoneField_1ZLPPNMJZD1J4", "bizAlias": "", "label": "电话", "placeholder": "请输入", "id": "PhoneField_15HTEUJ9RNNG0", "required": false, "mask": true, "_oriId": "PhoneField_15HTEUJ9RNNG0"}}, {"componentName": "IdCardField", "props": {"_extractId": "IdCardField_1XR8N5ZWY7EO0", "bizAlias": "", "label": "身份证", "placeholder": "请输入", "id": "IdCardField_1SO8PN95K3LS0", "required": false, "mask": true, "_oriId": "IdCardField_1SO8PN95K3LS0"}}, {"componentName": "RecipientAccountField", "props": {"_extractId": "RecipientAccountField_17FDXXY4GNNR4", "bizAlias": "", "label": "收款账户", "placeholder": "请选择", "id": "RecipientAccountField_17100SRPIUF40", "required": false, "mask": true, "_oriId": "RecipientAccountField_17100SRPIUF40"}}, {"componentName": "TimeAndLocationField", "props": {"_extractId": "TimeAndLocationField_JQS5HVSKR85C", "bizAlias": "", "label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_1AAYVFMC8NK00", "required": false, "mask": true, "_oriId": "TimeAndLocationField_1AAYVFMC8NK00"}}, {"children": [{"componentName": "TextField", "props": {"bizAlias": "name", "label": "姓名", "placeholder": "请输入", "id": "TextField_1WQNE44CDVUO0", "required": true}}, {"componentName": "IdCardField", "props": {"bizAlias": "idCardNo", "label": "身份证号码", "placeholder": "请输入", "id": "IdCardField_Y1R1XT05KZK0", "required": true}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "男", "key": "0"}, {"value": "女", "key": "1"}], "bizAlias": "gender", "label": "性别", "placeholder": "请选择", "id": "DDSelectField_13J3MISZUUPS0", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "nation", "label": "民族", "placeholder": "请输入", "id": "TextField_12HLZ9GALDKW0", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "birthdate", "label": "出生日期", "placeholder": "请选择", "id": "DDDateField_11DQO3YRN8SW0", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "address", "placeholder": "请输入", "label": "住址", "id": "TextField_19GYFE88XAQO0", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "issuingAuthority", "label": "签发机关", "placeholder": "请输入", "id": "TextField_VHCUADE9BCW0", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "expirationStartDate", "label": "有效期限起始时间", "placeholder": "请选择", "id": "DDDateField_P45GJA22BWW0", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "expirationEndDate", "label": "有效期限结束时间", "placeholder": "请选择", "id": "DDDateField_V7D1XT4S5OG0", "required": true}}], "componentName": "OcrIdCardField", "props": {"_extractId": "OcrIdCardField_1FDWXLSB0EADC", "options": ["name", "idCardNo", "gender", "nation", "birthdate", "address", "issuingAuthority", "expirationStartDate", "expirationEndDate"], "ocrType": "idcard", "label": "身份证识别", "placeholder": "识别出的信息会自动填充到表单", "id": "OcrIdCardField_1ULPW7Y5Q2RK0", "type": "ocr", "required": false, "_oriId": "OcrIdCardField_1ULPW7Y5Q2RK0"}}, {"children": [{"componentName": "PhoneField", "props": {"mode": "phone", "bizAlias": "", "label": "手机", "placeholder": "请输入", "id": "PhoneField_PEK0L8T2N9C0", "required": false, "mask": true}}, {"componentName": "PhoneField", "props": {"mode": "tel", "bizAlias": "", "label": "电话", "placeholder": "请输入", "id": "PhoneField_1NC9PY8H8PGG0", "required": false, "mask": true}}, {"componentName": "IdCardField", "props": {"bizAlias": "", "label": "身份证", "placeholder": "请输入", "id": "IdCardField_10QZCMKHHSPC0", "required": false, "mask": true}}, {"componentName": "TimeAndLocationField", "props": {"bizAlias": "", "label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_VA7ULYX7DCG0", "required": false, "mask": true}}], "componentName": "TableField", "props": {"tableViewMode": "table", "_extractId": "TableField_1XJUVK8JRERK", "label": "表格", "id": "TableField_XDEBW5P29C00", "actionName": "添加", "_oriId": "TableField_XDEBW5P29C00"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-CE7AF412-9801-493F-8A90-EA0BB5043E06", "appType": 0}}, "required": false, "multi": 0, "procType": ""}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 引用含明细 end ----------------------------------", "style": {"color": "red"}}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- 表格嵌FormRelateField引用+明细 start ----------------------------------", "style": {"color": "red"}}}, {"children": [{"componentName": "FormRelateField", "props": {"displayExtract": true, "quote": 1, "extract": false, "securityMode": 0, "label": "数据脱敏", "id": "FormRelateField_BA964WZ8V1MO", "title": "", "fields": [{"componentName": "PhoneField", "props": {"mode": "phone", "_extractId": "PhoneField_54ZJ45LFOLMO", "bizAlias": "", "label": "手机号", "placeholder": "请输入", "id": "PhoneField_DRUCX5NGWBK0", "required": false, "mask": true, "_oriId": "PhoneField_DRUCX5NGWBK0"}}, {"componentName": "PhoneField", "props": {"mode": "tel", "_extractId": "PhoneField_1ZLPPNMJZD1J4", "bizAlias": "", "label": "电话", "placeholder": "请输入", "id": "PhoneField_15HTEUJ9RNNG0", "required": false, "mask": true, "_oriId": "PhoneField_15HTEUJ9RNNG0"}}, {"componentName": "IdCardField", "props": {"_extractId": "IdCardField_1XR8N5ZWY7EO0", "bizAlias": "", "label": "身份证", "placeholder": "请输入", "id": "IdCardField_1SO8PN95K3LS0", "required": false, "mask": true, "_oriId": "IdCardField_1SO8PN95K3LS0"}}, {"componentName": "RecipientAccountField", "props": {"_extractId": "RecipientAccountField_17FDXXY4GNNR4", "bizAlias": "", "label": "收款账户", "placeholder": "请选择", "id": "RecipientAccountField_17100SRPIUF40", "required": false, "mask": true, "_oriId": "RecipientAccountField_17100SRPIUF40"}}, {"componentName": "TimeAndLocationField", "props": {"_extractId": "TimeAndLocationField_JQS5HVSKR85C", "bizAlias": "", "label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_1AAYVFMC8NK00", "required": false, "mask": true, "_oriId": "TimeAndLocationField_1AAYVFMC8NK00"}}, {"children": [{"componentName": "TextField", "props": {"bizAlias": "name", "label": "姓名", "placeholder": "请输入", "id": "TextField_1WQNE44CDVUO0", "required": true}}, {"componentName": "IdCardField", "props": {"bizAlias": "idCardNo", "label": "身份证号码", "placeholder": "请输入", "id": "IdCardField_Y1R1XT05KZK0", "required": true}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "男", "key": "0"}, {"value": "女", "key": "1"}], "bizAlias": "gender", "label": "性别", "placeholder": "请选择", "id": "DDSelectField_13J3MISZUUPS0", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "nation", "label": "民族", "placeholder": "请输入", "id": "TextField_12HLZ9GALDKW0", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "birthdate", "label": "出生日期", "placeholder": "请选择", "id": "DDDateField_11DQO3YRN8SW0", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "address", "placeholder": "请输入", "label": "住址", "id": "TextField_19GYFE88XAQO0", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "issuingAuthority", "label": "签发机关", "placeholder": "请输入", "id": "TextField_VHCUADE9BCW0", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "expirationStartDate", "label": "有效期限起始时间", "placeholder": "请选择", "id": "DDDateField_P45GJA22BWW0", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "expirationEndDate", "label": "有效期限结束时间", "placeholder": "请选择", "id": "DDDateField_V7D1XT4S5OG0", "required": true}}], "componentName": "OcrIdCardField", "props": {"_extractId": "OcrIdCardField_1FDWXLSB0EADC", "options": ["name", "idCardNo", "gender", "nation", "birthdate", "address", "issuingAuthority", "expirationStartDate", "expirationEndDate"], "ocrType": "idcard", "label": "身份证识别", "placeholder": "识别出的信息会自动填充到表单", "id": "OcrIdCardField_1ULPW7Y5Q2RK0", "type": "ocr", "required": false, "_oriId": "OcrIdCardField_1ULPW7Y5Q2RK0"}}, {"children": [{"componentName": "PhoneField", "props": {"mode": "phone", "bizAlias": "", "label": "手机", "placeholder": "请输入", "id": "PhoneField_PEK0L8T2N9C0", "required": false, "mask": true}}, {"componentName": "PhoneField", "props": {"mode": "tel", "bizAlias": "", "label": "电话", "placeholder": "请输入", "id": "PhoneField_1NC9PY8H8PGG0", "required": false, "mask": true}}, {"componentName": "IdCardField", "props": {"bizAlias": "", "label": "身份证", "placeholder": "请输入", "id": "IdCardField_10QZCMKHHSPC0", "required": false, "mask": true}}, {"componentName": "TimeAndLocationField", "props": {"bizAlias": "", "label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_VA7ULYX7DCG0", "required": false, "mask": true}}], "componentName": "TableField", "props": {"tableViewMode": "table", "_extractId": "TableField_1XJUVK8JRERK", "label": "表格", "id": "TableField_XDEBW5P29C00", "actionName": "添加", "_oriId": "TableField_XDEBW5P29C00"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-CE7AF412-9801-493F-8A90-EA0BB5043E06", "appType": 0}}, "required": false, "multi": 0, "procType": ""}}], "componentName": "TableField", "props": {"tableViewMode": "table", "label": "表格", "id": "TableField_GEU7L313V4LC", "actionName": "添加"}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- 表格嵌FormRelateField引用+明细 end ----------------------------------", "style": {"color": "red"}}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- 表格嵌FormRelateField拷贝+明细 start ----------------------------------", "style": {"color": "red"}}}, {"children": [{"componentName": "FormRelateField", "props": {"displayExtract": true, "securityMode": 0, "bizAlias": "", "label": "明细包含脱敏控件", "title": "", "required": false, "multi": 0, "quote": 0, "extract": true, "id": "FormRelateField_1A62IVA4F0QO0", "fields": [{"children": [{"componentName": "TextField", "props": {"label": "单行输入框", "placeholder": "请输入", "id": "TextField_1LG9WLT9RZEO0", "required": false, "ratio": 50}}, {"componentName": "AddressField", "props": {"needDetail": true, "bizAlias": "", "label": "省市区", "id": "AddressField_21T0DPW1QQCG0", "required": false, "mask": true}}], "componentName": "TableField", "props": {"tableViewMode": "list", "_extractId": "TableField_QRR36M9OQA80", "bizAlias": "", "label": "表格", "id": "TableField_QRR36M9OQA80", "actionName": "添加", "_oriId": "TableField_1V8635HS0S2O0"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-A4A1B585-C521-466C-8E93-1B6CCFB10EC3", "appType": 0}}, "procType": ""}}], "componentName": "TableField", "props": {"tableViewMode": "table", "label": "表格", "id": "TableField_778FNK6S6JO0", "actionName": "添加"}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- 表格嵌FormRelateField拷贝+明细 end ----------------------------------", "style": {"color": "red"}}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- FormRelateField CRM旧版 start ----------------------------------", "style": {"color": "red"}}}, {"componentName": "FormRelateField", "props": {"quote": 1, "relateSource": [{"bizType": "crm_customer", "fields": [{"componentName": "PhoneField", "props": {"mode": "phone", "bizAlias": "", "label": "客户电话脱敏", "placeholder": "请输入", "id": "PhoneField_15AHMKLGYMPS0", "required": false, "mask": true}}, {"componentName": "TextField", "props": {"bizAlias": "customer_name", "placeholder": "请输入组织全称", "label": "客户名称", "id": "TextField-K2U5DHAA", "required": true}}], "dataSource": {"type": "crm_customer", "params": {"filter": ""}, "target": {"appUuid": "SWAPP-B827CC2C2E02BC2839A2ACFFB6E7B95C", "bizType": "crm_customer", "formCode": "PROC-2627EB94-8FD1-43D0-AC1D-214CBF2E6819", "appType": 1}}}, {"bizType": "crm_customer_personal", "fields": [{"componentName": "TextField", "props": {"bizAlias": "customer_name", "id": "TextField-K2U5DHAA", "label": "客户姓名", "placeholder": "请输入客户姓名", "required": true}}, {"componentName": "PhoneField", "props": {"mode": "phone_tel", "bizAlias": "customer_phone", "id": "PhoneField_22EW6VK59RGG0", "label": "电话", "placeholder": "请输入", "required": true, "mask": true}}], "dataSource": {"type": "form", "params": {"filters": []}, "target": {"appUuid": "SWAPP-B827CC2C2E02BC2839A2ACFFB6E7B95C", "bizType": "crm_customer_personal", "formCode": "PROC-C3A05390-B119-4C24-A4B0-A2C35F11BD9A", "appType": 1}}}], "securityMode": 0, "relateBizType": "crm_customer_all", "label": "客户", "id": "FormRelateField_LBKD3IZRXT80", "title": "", "fields": [{"componentName": "PhoneField", "props": {"mode": "phone", "bizAlias": "", "label": "客户电话脱敏", "placeholder": "请输入", "id": "PhoneField_15AHMKLGYMPS0", "required": false, "mask": true}}, {"componentName": "TextField", "props": {"bizAlias": "customer_name", "placeholder": "请输入组织全称", "label": "客户名称", "id": "TextField-K2U5DHAA", "required": true}}], "dataSource": {"type": "crm_customer", "params": {"filter": ""}, "target": {"appUuid": "SWAPP-B827CC2C2E02BC2839A2ACFFB6E7B95C", "bizType": "crm_customer", "formCode": "PROC-2627EB94-8FD1-43D0-AC1D-214CBF2E6819", "appType": 1}}, "required": false, "multi": 0, "procType": ""}}, {"componentName": "TextNote", "props": {"id": "note", "content": "----------------- FormRelateField CRM旧版 end ----------------------------------", "style": {"color": "red"}}}]