[{"componentName": "DynamicField", "children": [{"componentName": "NumberField", "props": {"useThirdData": true, "notPrint": false, "id": "NumberField-RLJ-sWJoirB3", "label": "原基本工资", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "NumberField", "props": {"useThirdData": true, "notPrint": false, "id": "NumberField-RLJ-rgOtOb0W", "label": "原岗位", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "NumberField", "props": {"useThirdData": true, "notPrint": false, "id": "NumberField-RLJ-a1NLGcdZ", "label": "原薪资档案数字", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "TextField", "props": {"useThirdData": true, "notPrint": false, "id": "TextField-RLJ-P2zPB5vT", "label": "原岗位补贴2", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "TextField", "props": {"useThirdData": true, "notPrint": false, "id": "TextField-RLJ-lRG055qV", "label": "原个税", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "NumberField", "props": {"useThirdData": true, "notPrint": false, "id": "NumberField-RLJ-67r8giTi", "label": "原4567", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "TextField", "props": {"useThirdData": true, "notPrint": false, "id": "TextField-RLJ-eBHnQb1X", "label": "原45678eeeeeeeeeeew", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "TextField", "props": {"useThirdData": true, "notPrint": false, "id": "TextField-RLJ-o7XHwVnv", "label": "原备注", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "NumberField", "props": {"useThirdData": true, "notPrint": false, "id": "NumberField-RLJ-wPEPoVBy", "label": "原随便", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "TextField", "props": {"useThirdData": true, "notPrint": false, "id": "TextField-RLJ-71FGxmwi", "label": "原测试bug", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "TextField", "props": {"useThirdData": true, "notPrint": false, "id": "TextField-RLJ-Bi9nMs3s", "label": "原薪资档案文本", "placeholder": "请输入", "notExport": true, "required": false}}, {"componentName": "TextField", "props": {"useThirdData": true, "notPrint": false, "id": "TextField-RLJ-KEKQqlxl", "label": "原测试日期", "placeholder": "请输入", "notExport": true, "required": false}}], "props": {"bizAlias": "newSalary", "label": "新薪资信息", "id": "DynamicField-newsalary"}}, {"componentName": "IdCardField", "props": {"label": "身份证号", "placeholder": "请输入身份证", "required": false, "id": "IdCardField-testdd"}}, {"componentName": "PhoneField", "props": {"label": "手机号", "placeholder": "请输入手机号", "required": false, "disabled": true, "disableCountrySelect": true, "id": "PhoneField-testdd", "mode": "phone_tel"}}, {"children": [{"componentName": "NumberField", "props": {"unit": "万", "placeholder": "请输入数字", "label": "数字输入框", "id": "NumberField-K5LS1PP7"}}], "componentName": "TableField", "props": {"tableViewMode": "list", "statField": [], "label": "表格", "id": "TableField-K5LS1N2H", "actionName": "添加"}}, {"componentName": "TextField", "props": {"label": "单行输入框单行输入框", "placeholder": "请输入123", "autoComplete": ["输入示例123", "123456"], "required": false, "isRichTextEnable": true, "id": "TextField"}}, {"componentName": "TextareaField", "props": {"label": "多行输入框多行输入框", "placeholder": "请输入123", "required": false, "isRichTextEnable": true, "id": "TextareaField"}}, {"componentName": "NumberField", "props": {"label": "数字", "placeholder": "数字", "unit": "元", "required": false, "autoComplete": ["23456", "123456"], "id": "NumberField_K7BHQEFQ"}}, {"componentName": "MoneyField", "props": {"label": "金额", "placeholder": "金额", "required": false, "notUpper": "0", "id": "MoneyField"}}, {"componentName": "DDSelectField", "props": {"label": "单选题", "placeholder": "请选择", "options": [{"key": "option_0", "value": "选项1"}, {"key": "option_1", "value": "选项2"}, {"key": "option_2", "value": "选项3"}, {"key": "other", "value": "其它", "extension": {"image": ""}}], "required": false, "id": "DDSelectField_K7BHQCS3", "behaviorLinkage": []}}, {"componentName": "DDMultiSelectField", "props": {"label": "多选题", "placeholder": "请选择", "options": [{"key": "option_0", "value": "选项1"}, {"key": "option_1", "value": "选项2"}, {"key": "option_2", "value": "选项3"}, {"key": "other", "value": "其它", "extension": {"image": ""}}], "required": false, "validation": [{"type": "range", "param": [2, 4]}], "id": "DDMultiSelectField_K7BHQD99"}}, {"componentName": "ExternalContactField", "props": {"id": "ExternalContactField-J78F057H", "label": "外部联系人必填", "placeholder": "请选择", "required": false}}, {"componentName": "InnerContactField", "props": {"placeholder": "请选择", "label": "联系人", "id": "InnerContactField-K24EJ36D", "choice": 0, "required": false}}, {"componentName": "DepartmentField", "props": {"placeholder": "请选择", "label": "部门", "multiple": true, "required": false, "id": "DepartmentField-K27UCXBN"}}, {"componentName": "StarRatingField", "props": {"label": "请进行评分", "placeholder": "请输入", "limit": 5, "required": false, "id": "StarRatingField_K7BHQE24"}}, {"componentName": "StarRatingField", "props": {"label": "请进行评分", "placeholder": "请输入", "limit": 10, "required": false, "id": "StarRatingField_K7BJMMYG"}}, {"componentName": "CalculateField", "props": {"label": "计算公式", "placeholder": "计算公式", "required": false, "formula": "#{NumberField_K7BHQEFQ}", "id": "CalculateField_K7BHQEFQ"}}, {"componentName": "AddressField", "props": {"renderMode": "table", "required": false, "label": "省市区", "needDetail": false, "id": "AddressField_K7BHQET0", "placeholder": "省市区"}}, {"componentName": "AddressField", "props": {"renderMode": "table", "required": false, "label": "省市区", "needDetail": true, "id": "AddressField_K7BJMDR9", "placeholder": "省市区"}}, {"componentName": "TimeAndLocationField", "props": {"required": false, "hideTime": true, "label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_K7BHQF74"}}, {"componentName": "TextNote", "props": {"content": "说明文字123", "link": "", "watch": false, "id": "TextNote_K7BHQFM1"}}, {"componentName": "DDDateField", "props": {"label": "日期", "placeholder": "请选择1", "format": "yyyy-MM-dd", "unit": "天", "required": false, "id": "DDDateField_K7BHQFZ6"}}, {"componentName": "DDDateField", "props": {"label": "日期", "placeholder": "请选择2", "format": "yyyy-MM-dd HH:mm", "unit": "天", "required": false, "id": "DDDateField_K7BJMU8X"}}, {"componentName": "DDDateRangeField", "props": {"duration": true, "durationLabel": "天数", "format": "yyyy-MM-dd HH:mm", "id": "DDDateRangeField-K27UBLO8", "label": ["开始时间", "结束时间"], "placeholder": "请选择", "push": {"attendanceRule": 1}, "required": false, "stat": false, "unit": "天"}}, {"componentName": "DDDateRangeField", "props": {"duration": true, "durationLabel": "时长", "format": "yyyy-MM-dd", "id": "DDDateRangeField-K27UCXBS", "label": ["开始时间2", "结束时间2"], "placeholder": "请选择", "push": {"attendanceRule": 1}, "pushToCalendar": "0", "required": false, "unit": "天"}}, {"componentName": "DDPhotoField", "props": {"label": "图片", "watermark": false, "useAlbum": true, "required": false, "id": "DDPhotoField_K7BJP4A7"}}, {"componentName": "DDPhotoField", "props": {"label": "图片只能拍照", "watermark": false, "useAlbum": false, "required": false, "id": "DDPhotoField_K7BHQGC4"}}, {"componentName": "SignatureField", "props": {"id": "SignatureField", "label": "手写签名", "placeholder": "请在此签名", "required": false, "watermask": "手写签名手写签名", "sticker": {"time": 1586944503918, "username": "羽赫"}, "readFromLast": true}}, {"componentName": "MatrixRadioField", "props": {"label": "矩阵单选", "rows": [{"key": "row_1", "name": "行1", "alias": ""}, {"key": "row_K6WZA1DZ", "name": "行2", "alias": ""}], "cols": [{"key": "col_1", "name": "列1", "alias": ""}, {"key": "col_K6WZA26L", "name": "列2", "alias": ""}, {"key": "col_K6WZA2NB", "name": "列3", "alias": ""}, {"key": "col_K6WZA33J", "name": "列4", "alias": ""}, {"key": "col_K6WZA4BK", "name": "列5", "alias": ""}, {"key": "col_K7PZEGBA", "name": "列6", "alias": ""}, {"key": "col_K7PZEHBG", "name": "列7", "alias": ""}, {"key": "col_K7PZEHXQ", "name": "列8", "alias": ""}, {"key": "col_K7PZEIOA", "name": "列9", "alias": ""}], "required": false, "id": "MatrixRadioField_K6WE9BJ0"}}, {"componentName": "MatrixTextField", "props": {"label": "矩阵填空", "rows": [{"key": "row_1", "name": "行1", "alias": ""}, {"key": "row_K6WZ9UAQ", "name": "行2", "alias": ""}], "cols": [{"key": "col_1", "name": "列1", "alias": ""}, {"key": "col_K6WZ9QC3", "name": "列2", "alias": ""}, {"key": "col_K6WZ9TF4", "name": "列3", "alias": ""}, {"key": "col_K6WZ9VPP", "name": "列4", "alias": ""}, {"key": "col_K7PZESRI", "name": "列5", "alias": ""}, {"key": "col_K7PZETOX", "name": "列6", "alias": ""}, {"key": "col_K7PZEUD3", "name": "列7", "alias": ""}, {"key": "col_K7PZEVIH", "name": "列8", "alias": ""}, {"key": "col_K7PZEWDW", "name": "列9", "alias": ""}], "required": false, "id": "MatrixTextField_K6WE9FOD"}}]