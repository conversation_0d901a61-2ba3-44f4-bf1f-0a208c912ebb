[{"componentName": "MoneyField", "props": {"notUpper": "0", "label": "金额（元）", "placeholder": "请输入金额", "id": "MoneyField_F4FO48E2ZZ40", "required": false}}, {"componentName": "TimeAndLocationField", "props": {"label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_95URWC0WYCS0", "required": false}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "bizAlias": "", "label": "图片", "id": "DDPhotoField_217XPR82G1R40", "required": false}}, {"componentName": "TextField", "props": {"scan": true, "bizAlias": "", "label": "单行输入框", "placeholder": "请输入", "id": "TextField_212S3ZLJ96ZK0", "required": true, "ratio": 50}}, {"componentName": "TextareaField", "props": {"label": "多行输入框", "placeholder": "请输入", "id": "TextareaField_EHTO1QON55S0", "required": false}}, {"componentName": "NumberField", "props": {"label": "数字输入框", "placeholder": "请输入数字", "id": "NumberField_1A3ZSF5IQHNK0", "required": false, "ratio": 50}}, {"componentName": "DDSelectField", "props": {"rely": {}, "options": [{"value": "选项1", "key": "option_0"}, {"extension": {"image": "https://static.dingtalk.com/media/lQDPDht0EPpPofjNC9DND8CwoS7TPIpn12ACjtYULcDnAA_4032_3024.jpg"}, "value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}], "bizAlias": "", "label": "单选框", "placeholder": "请选择", "id": "DDSelectField_16N66R9TIPJ40", "fields": [], "dataSource": {}, "required": false, "spread": false, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "DDMultiSelectField", "props": {"rely": {}, "options": [{"extension": {"image": "https://static.dingtalk.com/media/lQDPDht0D5hCbn7NC9DND8Cwxpa_xN3FHi4CjtPQLMDWAA_4032_3024.jpg"}, "value": "13", "key": "option_0"}, {"extension": {"image": "https://static.dingtalk.com/media/lQDPDht0D21YGGvNC9DND8Cwo4n_F0Gcd1QCjtOJiICuAA_4032_3024.jpg"}, "value": "23", "key": "option_1"}, {"extension": {"image": "https://static.dingtalk.com/media/lQDPDht0D5vV8yPNC9DND8Cwtt-1oK0zaOICjtPVaECdAA_4032_3024.jpg"}, "value": "33", "key": "option_2"}, {"extension": {"image": ""}, "value": "其它", "key": "other"}], "bizAlias": "", "label": "多选框", "placeholder": "请选择", "id": "DDMultiSelectField_21AAOU398MDC0", "fields": [], "dataSource": {}, "required": false, "spread": false, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "DDSelectField", "props": {"rely": {}, "options": [{"value": "选项1", "key": "option_0"}, {"extension": {"image": "https://static.dingtalk.com/media/lQDPDht0EPpPofjNC9DND8CwoS7TPIpn12ACjtYULcDnAA_4032_3024.jpg"}, "value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}], "bizAlias": "", "label": "单选框", "placeholder": "请选择", "id": "DDSelectField_BVKEHI979PMO", "fields": [], "dataSource": {}, "required": true, "spread": true, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "DDMultiSelectField", "props": {"rely": {}, "options": [{"extension": {"image": "https://static.dingtalk.com/media/lQDPDht0D5hCbn7NC9DND8Cwxpa_xN3FHi4CjtPQLMDWAA_4032_3024.jpg"}, "value": "13", "key": "option_0"}, {"extension": {"image": "https://static.dingtalk.com/media/lQDPDht0D21YGGvNC9DND8Cwo4n_F0Gcd1QCjtOJiICuAA_4032_3024.jpg"}, "value": "23", "key": "option_1"}, {"extension": {"image": "https://static.dingtalk.com/media/lQDPDht0D5vV8yPNC9DND8Cwtt-1oK0zaOICjtPVaECdAA_4032_3024.jpg"}, "value": "33", "key": "option_2"}, {"extension": {"image": ""}, "value": "其它", "key": "other"}], "bizAlias": "", "label": "多选框", "placeholder": "请选择", "id": "DDMultiSelectField_17RBXOIM3V0N4", "fields": [], "dataSource": {}, "required": false, "spread": true, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "DDDateField", "props": {"unit": "小时", "format": "yyyy-MM-dd HH:mm", "bizAlias": "", "label": "日期", "placeholder": "请选择", "id": "DDDateField_1CIT8F4FWYGW0", "required": false}}, {"componentName": "DDDateRangeField", "props": {"duration": true, "unit": "小时", "format": "yyyy-MM-dd HH:mm", "bizAlias": "", "label": ["开始时间", "结束时间"], "placeholder": "请选择", "id": "DDDateRangeField_1U4SZR3T2WOW0", "durationLabel": "时长", "required": false}}, {"componentName": "TextNote", "props": {"notPrint": "0", "id": "TextNote_1CYO4M4QDPXC0", "content": "请输入说明文字"}}, {"componentName": "IdCardField", "props": {"label": "身份证", "placeholder": "请输入", "id": "IdCardField_1FHY2HCA2NQ80", "required": false}}, {"componentName": "PhoneField", "props": {"mode": "phone_tel", "bizAlias": "", "label": "电话", "placeholder": "请输入", "id": "PhoneField_JBPF2ITIRMG0", "required": false}}, {"componentName": "CascadeField", "props": {"require": true, "bizAlias": "", "label": "级联", "placeholder": "请输入", "id": "CascadeField_10OTD8I5WTIPS", "dataSource": {"type": "db_table", "target": {"appUuid": "", "sourceForm": "oa", "appId": "-4", "scene": "CascadeField_5684968"}}}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "label": "图片", "id": "DDPhotoField_ALINTC0690W0", "required": false}}, {"children": [{"componentName": "TextField", "props": {"label": "单行输入框", "placeholder": "请输入", "id": "TextField_1FB56SW071J40", "required": false, "ratio": 50}}], "componentName": "TableField", "props": {"tableViewMode": "table", "label": "表格", "id": "TableField_1XYKBV7J4VPC0", "actionName": "添加"}}, {"componentName": "DDAttachment", "props": {"label": "附件", "id": "DDAttachment_87WQKP1URLS0", "required": false}}, {"componentName": "SignatureField", "props": {"readFromLast": true, "label": "手写签名", "id": "SignatureField_I7AEZ5Z73TS0", "required": false}}, {"componentName": "ExternalContactField", "props": {"label": "外部联系人", "placeholder": "请选择", "id": "ExternalContactField_9PYVNHJYZTC0", "required": false}}, {"componentName": "InnerContactField", "props": {"label": "联系人", "placeholder": "请选择", "id": "InnerContactField_21ZDI87RAZ9C0", "choice": "1", "required": false}}, {"children": [{"componentName": "TextField", "props": {"id": "customer_name", "label": "客户名称"}}, {"componentName": "TextField", "props": {"id": "principal", "label": "负责人"}}, {"componentName": "TextField", "props": {"id": "participant", "label": "协同人"}}], "componentName": "OpenDataField", "multiple": false, "props": {"quote": 1, "label": "客户", "id": "OpenDataField_YUM57T6KFW1S", "fields": [{"componentName": "TextField", "props": {"id": "customer_name", "label": "客户名称"}}, {"componentName": "TextField", "props": {"id": "principal", "label": "负责人"}}, {"componentName": "TextField", "props": {"id": "participant", "label": "协同人"}}], "openDataSource": {"type": "spi", "key": "crmCustomerData"}, "required": false, "titleKey": "customer_name", "allowInput": false}, "reference": true}, {"children": [{"componentName": "TextField", "props": {"id": "contractName", "label": "合同名称"}}, {"componentName": "TextField", "props": {"id": "contractAmount", "label": "合同总额"}}, {"componentName": "TextField", "props": {"id": "contractRestAmount", "label": "待收/待付"}}, {"componentName": "TextField", "props": {"id": "contractOtherPartyList", "label": "对方主体"}}, {"componentName": "TextField", "props": {"id": "contractOurPartyList", "label": "我方主体"}}, {"componentName": "TextField", "props": {"id": "contractNo", "label": "合同编号"}}], "componentName": "OpenDataField", "multiple": true, "props": {"quote": 0, "multiple": true, "notPrint": 0, "label": "关联合同", "placeholder": "添加合同", "id": "OpenDataField_10LH55HLLOXDS", "fields": [{"componentName": "TextField", "props": {"id": "contractName", "label": "合同名称"}}, {"componentName": "TextField", "props": {"id": "contractAmount", "label": "合同总额"}}, {"componentName": "TextField", "props": {"id": "contractRestAmount", "label": "待收/待付"}}, {"componentName": "TextField", "props": {"id": "contractOtherPartyList", "label": "对方主体"}}, {"componentName": "TextField", "props": {"id": "contractOurPartyList", "label": "我方主体"}}, {"componentName": "TextField", "props": {"id": "contractNo", "label": "合同编号"}}], "openDataSource": {"type": "spi", "key": "contract_open_data"}, "titleKey": "contractName"}, "reference": false}, {"componentName": "DepartmentField", "props": {"multiple": false, "label": "部门", "placeholder": "请选择", "id": "DepartmentField_1F422H2BREQO0", "required": false}}, {"componentName": "IndustryDepartmentField", "props": {"multiple": false, "label": "行业通讯录部门", "placeholder": "请选择", "id": "IndustryDepartmentField_2P2EM5ODW01S", "required": false}}, {"componentName": "TimeAndLocationField", "props": {"label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_1DC9W079SCN40", "required": false}}, {"componentName": "CalculateField", "props": {"notUpper": "0", "label": "计算公式", "placeholder": "自动计算数值", "id": "CalculateField_7POVATHQ0640"}}, {"componentName": "InvoiceField", "props": {"appId": "78641", "invoiceChecking": true, "label": "发票", "id": "InvoiceField_1G6X56B6WBSW0", "required": false}}, {"componentName": "RelateField", "props": {"notPrint": "1", "label": "关联审批单", "placeholder": "请选择", "id": "RelateField_JMF77YC95EW0", "required": false}}, {"componentName": "AddressField", "props": {"needDetail": 0, "bizAlias": "", "label": "省市", "id": "AddressField_TZA7I2NQT9MO", "required": false}}, {"componentName": "AddressField", "props": {"needDetail": false, "label": "省市区", "id": "AddressField_180X5DQ9U3400", "required": false}}, {"componentName": "AddressField", "props": {"needDetail": true, "bizAlias": "", "label": "省市区-街道", "id": "AddressField_F12USQ74FHFK", "required": false}}, {"componentName": "StarRatingField", "props": {"limit": 5, "label": "评分", "placeholder": "请输入", "id": "StarRatingField_QJRRPWL9HJK0", "required": false}}, {"componentName": "StarRatingField", "props": {"limit": 10, "bizAlias": "", "label": "评分", "placeholder": "请输入", "id": "StarRatingField_1IB1OYI0G2680", "required": false}}, {"componentName": "RecipientAccountField", "props": {"label": "收款账户", "placeholder": "请选择", "id": "RecipientAccountField_ZTKEZOQKFKZK", "required": true}}, {"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}], "label": "流水号", "id": "SeqNumberField_111UQU9U9X9MO"}}, {"children": [{"componentName": "TextField", "props": {"bizAlias": "name", "label": "姓名", "placeholder": "请输入", "id": "TextField_11ISJ6Z41LOG0", "required": false}}, {"componentName": "IdCardField", "props": {"bizAlias": "idCardNo", "label": "身份证号码", "placeholder": "请输入", "id": "IdCardField_6LXW5B73OLFK", "required": false}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "男", "key": "0"}, {"value": "女", "key": "1"}], "bizAlias": "gender", "label": "性别", "placeholder": "请选择", "id": "DDSelectField_1XFDZZ86VPRSW", "required": false}}, {"componentName": "TextField", "props": {"bizAlias": "nation", "label": "民族", "placeholder": "请输入", "id": "TextField_1GMJRNATXIRCW", "required": false}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "birthdate", "label": "出生日期", "placeholder": "请选择", "id": "DDDateField_1DF2WVE8FWNWG", "required": false}}, {"componentName": "TextField", "props": {"bizAlias": "address", "placeholder": "请输入", "label": "住址", "id": "TextField_2032YJJBC0E80", "required": false}}, {"componentName": "TextField", "props": {"bizAlias": "issuingAuthority", "label": "签发机关", "placeholder": "请输入", "id": "TextField_3E0787G0YLJ4", "required": false}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "expirationStartDate", "label": "有效期限起始时间", "placeholder": "请选择", "id": "DDDateField_NZHHW0GTSQGW", "required": false}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "expirationEndDate", "label": "有效期限结束时间", "placeholder": "请选择", "id": "DDDateField_MHL2EKAAEXA8", "required": false}}], "componentName": "OcrIdCardField", "props": {"options": ["name", "idCardNo", "gender", "nation", "birthdate", "address", "issuingAuthority", "expirationStartDate", "expirationEndDate"], "ocrType": "idcard", "label": "身份证识别", "placeholder": "识别出的信息会自动填充到表单", "id": "OcrIdCardField_1A2HJAPKVLHC0", "type": "ocr", "required": false}}, {"componentName": "OcrTextField", "props": {"ocrType": "advanced", "label": "通用文字识别", "placeholder": "请输入", "id": "OcrTextField_OJKINGQQHPMO", "type": "ocr", "required": false}}]