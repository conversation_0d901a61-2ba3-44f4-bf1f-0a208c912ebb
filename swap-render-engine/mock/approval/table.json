[{"children": [{"componentName": "TextField", "props": {"bizAlias": "", "label": "单行输入框-必填单行输入框-必填", "placeholder": "请输入", "id": "TextField_1VAR8GN4L3B40", "required": true, "ratio": 50}}, {"componentName": "TextareaField", "props": {"rely": {"formula": "", "type": "rely", "fields": ["TextField_1VAR8GN4L3B40"]}, "defaultValue": "", "bizAlias": "", "label": "多行输入框", "placeholder": "单行=单行-表格内时，多行=单行", "id": "TextareaField_1AGEBOVYSMCCG", "fields": [{"props": {"id": "TextField-K2AD4O5B"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "TextField_1VAR8GN4L3B40", "fieldId": "TextField-K2AD4O5B"}]}, "target": {"appUuid": "dingd462a35379ac8d75f2c783f7214b6d69", "formCode": "PROC-D73AD539-688A-43CD-9C2F-78867F6EF293"}}, "required": false}}, {"componentName": "NumberField", "props": {"rely": {"formula": "${NumberField_1UQBEVZ2CJOQO}=${MoneyField_1T3G67Y1YGZ5S}*2", "type": "formula", "fields": ["NumberField_1UQBEVZ2CJOQO", "MoneyField_1T3G67Y1YGZ5S"], "version": 2}, "bizAlias": "", "label": "数字输入框=金额*3", "placeholder": "请输入数字", "id": "NumberField_H0P8FBJOLHQ8", "fields": [], "dataSource": {}, "required": false, "ratio": 50}}, {"componentName": "MoneyField", "props": {"notUpper": "0", "precision": 3, "bizAlias": "", "label": "金额（元）", "placeholder": "请输入金额", "id": "MoneyField_1Z5GJQNBGFQ8", "required": false}}, {"componentName": "DDSelectField", "props": {"defaultValue": "选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4", "bizAlias": "", "label": "单选框", "required": false, "spread": false, "rely": {}, "options": [{"value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}, {"value": "选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4", "key": "option_106BJY97O0NPC"}], "placeholder": "请选择", "id": "DDSelectField_1EA6G5V6L1YWW", "fields": [], "dataSource": {}, "defaultExtendValue": {"label": "选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4", "key": "option_106BJY97O0NPC"}, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "DDMultiSelectField", "props": {"defaultValue": ["选项1"], "bizAlias": "", "label": "多选框", "required": false, "spread": false, "rely": {}, "options": [{"value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}, {"value": "选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4选项4", "key": "option_23JWNR74F864G"}], "placeholder": "请选择", "id": "DDMultiSelectField_TNUMN5TJUG3K", "fields": [], "dataSource": {}, "defaultExtendValue": [{"key": "option_0"}], "ratio": 50, "behaviorLinkage": []}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "", "label": "日期", "placeholder": "请选择", "id": "DDDateField_4F2VP3TJTREO", "required": false}}, {"componentName": "DDDateRangeField", "props": {"duration": false, "unit": "天", "format": "yyyy-MM-dd", "label": ["开始时间", "结束时间"], "placeholder": "请选择", "id": "DDDateRangeField_GQ7BUMYATZ40", "durationLabel": "时长", "required": false}}, {"componentName": "IdCardField", "props": {"label": "身份证", "placeholder": "请输入", "id": "IdCardField_ZY8ZJ2886IO0", "required": false}}, {"componentName": "PhoneField", "props": {"mode": "phone_tel", "bizAlias": "", "label": "电话", "placeholder": "请输入", "id": "PhoneField_Z2V0I61BKQ2O", "required": false}}, {"componentName": "StarRatingField", "props": {"limit": 10, "bizAlias": "", "label": "评分", "placeholder": "请输入", "id": "StarRatingField_N077NYOLGA2O", "required": false}}, {"componentName": "TextNote", "props": {"notPrint": "0", "bizAlias": "", "style": {"color": "#F25643"}, "id": "TextNote_1J5G4CLA6H8N4", "content": "说明文字"}}, {"componentName": "CascadeField", "props": {"require": true, "bizAlias": "", "label": "产品分类", "placeholder": "请输入", "id": "CascadeField_TX353XNGABR4", "dataSource": {"type": "db_table", "target": {"appUuid": "", "sourceForm": "oa", "appId": "-4", "scene": "CascadeField_6615001", "initScene": "CascadeField_6615001"}}}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "label": "图片", "id": "DDPhotoField_1QB7NWI9RMYO0", "required": false}}, {"componentName": "DDAttachment", "props": {"label": "附件", "id": "DDAttachment_1N5S2DYHMX0QO", "required": false}}, {"componentName": "ExternalContactField", "props": {"label": "外部联系人", "placeholder": "请选择", "id": "ExternalContactField_1AQOUEVBETFY8", "required": false}}, {"componentName": "InnerContactField", "props": {"label": "联系人", "placeholder": "请选择", "id": "InnerContactField_SCDVUIETVC3K", "choice": "0", "required": false}}, {"reference": true, "children": [{"componentName": "TextField", "props": {"id": "customer_name", "label": "客户名称"}}, {"componentName": "TextField", "props": {"id": "principal", "label": "负责人"}}, {"componentName": "TextField", "props": {"id": "participant", "label": "协同人"}}], "multiple": false, "componentName": "OpenDataField", "props": {"quote": 1, "label": "客户", "id": "OpenDataField_OWNO2DCD5EKG", "fields": [{"componentName": "TextField", "props": {"id": "customer_name", "label": "客户名称"}}, {"componentName": "TextField", "props": {"id": "principal", "label": "负责人"}}, {"componentName": "TextField", "props": {"id": "participant", "label": "协同人"}}], "openDataSource": {"type": "spi", "key": "crmCustomerData"}, "required": false, "titleKey": "customer_name", "allowInput": false}}, {"componentName": "DepartmentField", "props": {"multiple": false, "label": "部门", "placeholder": "请选择", "id": "DepartmentField_NCA12UMC1C74", "required": false}}, {"componentName": "IndustryDepartmentField", "props": {"multiple": false, "label": "行业通讯录部门", "placeholder": "请选择", "id": "IndustryDepartmentField_1SV00MNYIM0W0", "required": false}}, {"componentName": "TimeAndLocationField", "props": {"label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_1GTSI10XGCC1S", "required": false}}, {"componentName": "CalculateField", "props": {"notUpper": "1", "bizAlias": "", "label": "计算公式", "placeholder": "自动计算数值", "id": "CalculateField_QEVPTQ1HU8ZK"}}, {"componentName": "RelateField", "props": {"notPrint": "1", "label": "关联审批单", "placeholder": "请选择", "id": "RelateField_1C889ZVVBLIIO", "required": false}}, {"componentName": "InvoiceField", "props": {"invoiceChecking": true, "appId": "78641", "label": "发票", "id": "InvoiceField_SXPID02QSKCG", "required": false}}, {"reference": false, "children": [{"componentName": "TextField", "props": {"id": "contractName", "label": "合同名称"}}, {"componentName": "TextField", "props": {"id": "contractAmount", "label": "合同总额"}}, {"componentName": "TextField", "props": {"id": "contractRestAmount", "label": "待收/待付"}}, {"componentName": "TextField", "props": {"id": "contractOtherPartyList", "label": "对方主体"}}, {"componentName": "TextField", "props": {"id": "contractOurPartyList", "label": "我方主体"}}, {"componentName": "TextField", "props": {"id": "contractNo", "label": "合同编号"}}], "multiple": true, "componentName": "OpenDataField", "props": {"quote": 0, "multiple": true, "notPrint": 0, "label": "关联合同", "placeholder": "添加合同", "id": "OpenDataField_2KH3AZP2KP0", "fields": [{"componentName": "TextField", "props": {"id": "contractName", "label": "合同名称"}}, {"componentName": "TextField", "props": {"id": "contractAmount", "label": "合同总额"}}, {"componentName": "TextField", "props": {"id": "contractRestAmount", "label": "待收/待付"}}, {"componentName": "TextField", "props": {"id": "contractOtherPartyList", "label": "对方主体"}}, {"componentName": "TextField", "props": {"id": "contractOurPartyList", "label": "我方主体"}}, {"componentName": "TextField", "props": {"id": "contractNo", "label": "合同编号"}}], "openDataSource": {"type": "spi", "key": "contract_open_data"}, "titleKey": "contractName"}}, {"componentName": "AddressField", "props": {"needDetail": false, "label": "省市区", "id": "AddressField_UDEYZR17Z0G0", "required": false}}, {"componentName": "FormRelateField", "props": {"displayExtract": true, "quote": 1, "extract": false, "securityMode": 0, "label": "外出", "id": "FormRelateField_1GSS9KSFRW8W0", "title": "", "fields": [{"componentName": "TextareaField", "props": {"_extractId": "TextareaField_2387FIEGTEGHS", "label": "外出事由", "placeholder": "请输入外出事由", "id": "外出事由", "required": false, "_oriId": "外出事由"}}, {"componentName": "DDPhotoField", "props": {"_extractId": "DDPhotoField_2NOL00052474", "label": "图片", "id": "图片", "_oriId": "图片"}}, {"componentName": "DDSelectField", "props": {"_extractId": "DDSelectField_94XSGJF16ADC", "options": [{"extension": {"unit": "hour"}, "value": "选项", "key": "option_QQC6LAJHVEV4"}], "bizAlias": "type", "label": "外出类型", "id": "DDSelectField-K2BJKT32", "required": false, "_oriId": "DDSelectField-K2BJKT32"}}, {"componentName": "DDDateField", "props": {"_extractId": "DDDateField_1MFMR0KYEJRI8", "bizAlias": "startTime", "placeholder": "请选择", "label": "开始时间", "id": "DDDateField-K2BJKT33", "required": false, "_oriId": "DDDateField-K2BJKT33"}}, {"componentName": "DDDateField", "props": {"_extractId": "DDDateField_1L1A4K2GDHVR4", "bizAlias": "finishTime", "placeholder": "请选择", "label": "结束时间", "id": "DDDateField-K2BJKT34", "required": false, "_oriId": "DDDateField-K2BJKT34"}}, {"componentName": "NumberField", "props": {"_extractId": "NumberField_18GPKZUDIV37K", "disable": true, "bizAlias": "duration", "placeholder": "请输入时长", "label": "时长", "id": "NumberField-K2BJKT35", "required": false, "_oriId": "NumberField-K2BJKT35"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-37F9F1E8-F7AE-47FE-A6CA-1548DD655058", "appType": 0}}, "required": false, "multi": 0, "procType": ""}}, {"componentName": "FormRelateField", "props": {"displayExtract": true, "quote": 1, "extract": false, "securityMode": 0, "label": "明细", "id": "FormRelateField_1WGJYMQ276EWW", "title": "", "fields": [{"children": [{"componentName": "TextField", "props": {"label": "单行输入框", "placeholder": "请输入", "id": "TextField_34V8DJD57VY8", "required": false, "ratio": 50}}, {"componentName": "TextareaField", "props": {"label": "多行输入框", "placeholder": "请输入", "id": "TextareaField_1K13915GHCF0G", "required": false}}, {"componentName": "NumberField", "props": {"label": "数字输入框", "placeholder": "请输入数字", "id": "NumberField_131DKUXH0ITXC", "required": false, "ratio": 50}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}], "label": "单选框", "placeholder": "请选择", "id": "DDSelectField_2WGY4U2BQE2O", "required": false, "spread": false, "ratio": 50}}, {"componentName": "DDMultiSelectField", "props": {"options": [{"value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}], "label": "多选框", "placeholder": "请选择", "id": "DDMultiSelectField_YYOK72XWYDQ8", "required": false, "spread": false, "ratio": 50}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "label": "日期", "placeholder": "请选择", "id": "DDDateField_KRI89BRHS0SG", "required": false}}, {"componentName": "DDDateRangeField", "props": {"duration": false, "unit": "天", "format": "yyyy-MM-dd", "label": ["开始时间", "结束时间"], "placeholder": "请选择", "id": "DDDateRangeField_18WLK6JSQ794W", "durationLabel": "时长", "required": false}}, {"componentName": "IdCardField", "props": {"label": "身份证", "placeholder": "请输入", "id": "IdCardField_JA3TGDZ5730G", "required": false}}, {"componentName": "PhoneField", "props": {"mode": "phone", "label": "电话", "placeholder": "请输入", "id": "PhoneField_1TDYGZ2E6WQGW", "required": false}}, {"componentName": "CascadeField", "props": {"require": true, "bizAlias": "", "label": "产品分类", "placeholder": "请输入", "id": "CascadeField_1ZJPCBGOGFG8W", "dataSource": {"type": "db_table", "target": {"appUuid": "dingd462a35379ac8d75f2c783f7214b6d69", "sourceForm": "oa", "appId": "-4", "scene": "CascadeField_6611001", "initScene": "CascadeField_6611001"}}}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "label": "图片", "id": "DDPhotoField_1D16F58MR8S8W", "required": false}}, {"componentName": "MoneyField", "props": {"notUpper": "1", "bizAlias": "", "label": "金额（元）", "placeholder": "请输入金额", "id": "MoneyField_YW5OFNH8HVY8", "required": false}}, {"componentName": "DDAttachment", "props": {"label": "附件", "id": "DDAttachment_1Q0FWUW0OKXS0", "required": false}}, {"componentName": "ExternalContactField", "props": {"label": "外部联系人", "placeholder": "请选择", "id": "ExternalContactField_1P8N0XUOLT1XC", "required": false}}, {"componentName": "InnerContactField", "props": {"label": "联系人", "placeholder": "请选择", "id": "InnerContactField_4745IOHZF9VK", "choice": "0", "required": false}}, {"componentName": "DepartmentField", "props": {"multiple": false, "label": "部门", "placeholder": "请选择", "id": "DepartmentField_CHH7469GVE2O", "required": false}}, {"componentName": "IndustryDepartmentField", "props": {"multiple": false, "label": "行业通讯录部门", "placeholder": "请选择", "id": "IndustryDepartmentField_1KAQC43WHBO5C", "required": false}}, {"componentName": "TimeAndLocationField", "props": {"label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_QDVDTVFYADC0", "required": false}}, {"componentName": "CalculateField", "props": {"notUpper": "1", "bizAlias": "", "label": "计算公式", "placeholder": "自动计算数值", "id": "CalculateField_1F18Y3LIIUYGW"}}, {"componentName": "InvoiceField", "props": {"invoiceChecking": true, "appId": "78641", "label": "发票", "id": "InvoiceField_4IHGUQV1TZLS", "required": false}}, {"componentName": "RelateField", "props": {"notPrint": "1", "label": "关联审批单", "placeholder": "请选择", "id": "RelateField_XMYK75HT2BK0", "required": false}}, {"componentName": "AddressField", "props": {"needDetail": false, "label": "省市区", "id": "AddressField_PSEJG38619TS", "required": false}}, {"componentName": "AddressField", "props": {"needDetail": false, "label": "省市区", "id": "AddressField_1KWPZJA06UD4W", "required": false}}, {"componentName": "StarRatingField", "props": {"limit": 5, "label": "评分", "placeholder": "请输入", "id": "StarRatingField_RK930QQZUIV4", "required": false}}], "componentName": "TableField", "props": {"enabledBatchExport": true, "enabledExpandRow": true, "enabledPagination": true, "tableViewMode": "table", "stickyHeadRow": true, "enabledUnique": true, "enabledFullScreen": true, "pageSize": 5, "bizAlias": "", "label": "表格", "enabledCustomColWidth": true, "_oriId": "TableField_1EFHVB485EGW", "_extractId": "TableField_QW4TTI3U2FB4", "enabledUpAndDown": true, "id": "TableField_1EFHVB485EGW", "enabledBatchImport": true, "enabledClearAll": true, "actionName": "添加", "stickyColumns": 2}}, {"componentName": "TextField", "props": {"_extractId": "TextField_12ZW0JG3B9DKW", "label": "单行输入框", "placeholder": "请输入", "id": "TextField_EZGUYZM8HIWW", "required": false, "ratio": 50, "_oriId": "TextField_EZGUYZM8HIWW"}}, {"componentName": "TextareaField", "props": {"_extractId": "TextareaField_1DQZ8C8ICWX6O", "label": "多行输入框", "placeholder": "请输入", "id": "TextareaField_C8QPKL60JSG", "required": false, "_oriId": "TextareaField_C8QPKL60JSG"}}, {"componentName": "NumberField", "props": {"_extractId": "NumberField_N1PKCBSKU41S", "label": "数字输入框", "placeholder": "请输入数字", "id": "NumberField_1E0NHYI2OREV4", "required": false, "ratio": 50, "_oriId": "NumberField_1E0NHYI2OREV4"}}, {"componentName": "DDSelectField", "props": {"_extractId": "DDSelectField_1A0HNOWXOW6BK", "options": [{"value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}], "label": "单选框", "placeholder": "请选择", "id": "DDSelectField_IDAXTGKMBEGW", "required": false, "spread": false, "ratio": 50, "_oriId": "DDSelectField_IDAXTGKMBEGW"}}, {"componentName": "DDMultiSelectField", "props": {"_extractId": "DDMultiSelectField_1R0IY51FR0OOW", "options": [{"value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}], "label": "多选框", "placeholder": "请选择", "id": "DDMultiSelectField_1PY85NBE0NVNK", "required": false, "spread": false, "ratio": 50, "_oriId": "DDMultiSelectField_1PY85NBE0NVNK"}}, {"componentName": "DDDateField", "props": {"unit": "天", "_extractId": "DDDateField_H4LU9H3NHTZ4", "format": "yyyy-MM-dd", "label": "日期", "placeholder": "请选择", "id": "DDDateField_1QF74JBV950QO", "required": false, "_oriId": "DDDateField_1QF74JBV950QO"}}, {"componentName": "DDDateRangeField", "props": {"duration": false, "unit": "天", "_extractId": "DDDateRangeField_18OKRKBXEGIKG", "format": "yyyy-MM-dd", "label": ["开始时间", "结束时间"], "placeholder": "请选择", "id": "DDDateRangeField_OZ5V0J90PLA8", "durationLabel": "时长", "required": false, "_oriId": "DDDateRangeField_OZ5V0J90PLA8"}}, {"componentName": "IdCardField", "props": {"_extractId": "IdCardField_RJHWOKDF0VSW", "label": "身份证", "placeholder": "请输入", "id": "IdCardField_19ALHZ77Q1MV4", "required": false, "_oriId": "IdCardField_19ALHZ77Q1MV4"}}, {"componentName": "PhoneField", "props": {"mode": "phone", "_extractId": "PhoneField_168GUGQXUS3R4", "label": "电话", "placeholder": "请输入", "id": "PhoneField_1DS6K0CHIGUF4", "required": false, "_oriId": "PhoneField_1DS6K0CHIGUF4"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-E3A070D0-0506-47C3-91F0-38900F9BA039", "appType": 1}}, "required": false, "multi": 0, "procType": ""}}], "componentName": "TableField", "props": {"enabledBatchExport": true, "enabledExpandRow": true, "enabledPagination": true, "tableViewMode": "table", "stickyHeadRow": true, "statField": [{"payEnable": false, "upper": true, "id": "MoneyField_1Z5GJQNBGFQ8", "label": "金额（元）-限制3个小数"}], "enabledUnique": true, "enabledFullScreen": true, "bizAlias": "", "label": "表格\\/:*?”<>|", "enabledCustomColWidth": true, "enabledUpAndDown": true, "customColWidthMap": {"NumberField_H0P8FBJOLHQ8": 100.9, "DDAttachment_1N5S2DYHMX0QO": 100, "AddressField_UDEYZR17Z0G0": 100, "DDDateField_4F2VP3TJTREO": 100, "IndustryDepartmentField_1SV00MNYIM0W0": 100, "DDSelectField_1EA6G5V6L1YWW": 100, "StarRatingField_N077NYOLGA2O": 100, "RelateField_1C889ZVVBLIIO": 100, "DDDateRangeField_GQ7BUMYATZ40": 100, "MoneyField_1Z5GJQNBGFQ8": 100, "TextareaField_1AGEBOVYSMCCG": 100, "ExternalContactField_1AQOUEVBETFY8": 100, "DDPhotoField_1QB7NWI9RMYO0": 100, "DepartmentField_NCA12UMC1C74": 100, "DDMultiSelectField_TNUMN5TJUG3K": 100, "CascadeField_TX353XNGABR4": 100, "OpenDataField_OWNO2DCD5EKG": 100, "PhoneField_Z2V0I61BKQ2O": 100, "InnerContactField_SCDVUIETVC3K": 100, "TimeAndLocationField_1GTSI10XGCC1S": 100, "TextNote_1J5G4CLA6H8N4": 100, "InvoiceField_SXPID02QSKCG": 100, "CalculateField_QEVPTQ1HU8ZK": 100, "OpenDataField_2KH3AZP2KP0": 100, "IdCardField_ZY8ZJ2886IO0": 100, "TextField_1VAR8GN4L3B40": 100, "FormRelateField_1GSS9KSFRW8W0": 100, "FormRelateField_1WGJYMQ276EWW": 100}, "id": "TableField_XQSLVGKVWL4W", "enabledBatchImport": true, "enabledClearAll": true, "actionName": "添加", "pageSize": 3, "maxCount": 10, "stickyColumns": 2}}, {"componentName": "DDAttachment", "props": {"label": "附件", "maxUpload": 30, "actionName": "上传附件", "id": "DDAttachment_20ZZVGWIZYQO0", "required": false}}, {"componentName": "PhoneField", "props": {"mode": "phone", "label": "电话", "placeholder": "请输入", "id": "PhoneField_1", "required": false}}, {"componentName": "PhoneField", "props": {"mode": "tel", "label": "电话", "placeholder": "请输入", "id": "PhoneField_2", "required": false}}, {"componentName": "PhoneField", "props": {"mode": "phone_tel", "label": "电话", "placeholder": "请输入", "id": "PhoneField_3", "required": false}}]