[{"componentName": "TextField", "props": {"placeholder": "请输入", "label": "单行输入框", "id": "TextField-K2AD4O5B", "viewMode": false}}, {"componentName": "TextareaField", "props": {"label": "多行输入框", "placeholder": "请输入", "id": "TextareaField_1A690U9UCI29S", "required": false}}, {"componentName": "NumberField", "props": {"label": "数字输入框", "placeholder": "请输入数字", "id": "NumberField_HYOCQQBXIARK", "required": false, "ratio": 50}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}], "label": "单选框", "placeholder": "请选择", "id": "DDSelectField_1K1E6HV08SF7K", "required": false, "spread": false, "ratio": 50}}, {"componentName": "DDMultiSelectField", "props": {"options": [{"value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}], "label": "多选框", "placeholder": "请选择", "id": "DDMultiSelectField_23WS4OH20MTQ8", "required": false, "spread": false, "ratio": 50}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "label": "日期", "placeholder": "请选择", "id": "DDDateField_QPAO274JMVI8", "required": false}}, {"componentName": "DDDateRangeField", "props": {"duration": false, "unit": "天", "format": "yyyy-MM-dd", "label": ["开始时间", "结束时间"], "placeholder": "请选择", "id": "DDDateRangeField_15ZDNEY5KNHFK", "durationLabel": "时长", "required": false}}, {"componentName": "DDDateRangeField", "props": {"duration": true, "unit": "天", "format": "yyyy-MM-dd", "bizAlias": "", "label": ["开始时间", "结束时间"], "placeholder": "请选择", "id": "DDDateRangeField_1ZCT44D5E4WLC", "durationLabel": "时长", "required": false}}, {"componentName": "CascadeField", "props": {"require": true, "bizAlias": "", "label": "产品分类", "placeholder": "请输入", "id": "CascadeField_IFHPL0FAYEIO", "dataSource": {"type": "db_table", "target": {"appUuid": "", "sourceForm": "oa", "appId": "-4", "scene": "CascadeField_6668003", "initScene": "CascadeField_6668003"}}}}, {"componentName": "TextNote", "props": {"notPrint": "0", "id": "TextNote_KRJSZ5C5HW5C", "content": "请输入说明文字"}}, {"componentName": "IdCardField", "props": {"label": "身份证", "placeholder": "请输入", "id": "IdCardField_22CQ3YUUKTNGG", "required": false}}, {"componentName": "PhoneField", "props": {"mode": "phone", "label": "电话", "placeholder": "请输入", "id": "PhoneField_1ZZT3JDUMOA9S", "required": false}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "label": "图片", "id": "DDPhotoField_5FVWS6YRNYM8", "required": false}}, {"children": [{"componentName": "TextField", "props": {"label": "单行输入框", "placeholder": "请输入", "id": "TextField_DNKUQYMKUI2O", "required": false, "ratio": 50}}], "componentName": "TableField", "props": {"tableViewMode": "table", "label": "表格", "id": "TableField_1EMRKW5WU55A8", "actionName": "添加"}}, {"componentName": "MoneyField", "props": {"notUpper": "0", "label": "金额（元）", "placeholder": "请输入金额", "id": "MoneyField_1Q1G8UT7EESXS", "required": false}}, {"componentName": "DDAttachment", "props": {"label": "附件", "id": "DDAttachment_F37URH99B5KW", "required": false}}, {"componentName": "SignatureField", "props": {"readFromLast": true, "label": "手写签名", "id": "SignatureField_Z4R8ZIOAK8W0", "required": false}}, {"componentName": "ExternalContactField", "props": {"label": "外部联系人", "placeholder": "请选择", "id": "ExternalContactField_1DG2CZ00X3MYO", "required": false}}, {"componentName": "InnerContactField", "props": {"label": "联系人", "placeholder": "请选择", "id": "InnerContactField_VYM8XR97GV0G", "choice": "0", "required": false}}, {"reference": true, "children": [{"componentName": "TextField", "props": {"id": "customer_name", "label": "客户名称"}}, {"componentName": "TextField", "props": {"id": "principal", "label": "负责人"}}, {"componentName": "TextField", "props": {"id": "participant", "label": "协同人"}}], "multiple": false, "componentName": "OpenDataField", "props": {"quote": 1, "label": "客户", "id": "OpenDataField_14W28AJTFOOAO", "fields": [{"componentName": "TextField", "props": {"id": "customer_name", "label": "客户名称"}}, {"componentName": "TextField", "props": {"id": "principal", "label": "负责人"}}, {"componentName": "TextField", "props": {"id": "participant", "label": "协同人"}}], "openDataSource": {"type": "spi", "key": "crmCustomerData"}, "required": false, "titleKey": "customer_name", "allowInput": false}}, {"reference": false, "children": [{"componentName": "TextField", "props": {"hideInDesigner": true, "bizAlias": "nameODF", "id": "name", "label": "预算名称"}}, {"componentName": "TextField", "props": {"hideInDesigner": true, "bizAlias": "ruleNameAllODF", "id": "ruleNameAll", "label": "预算单元"}}, {"componentName": "TextField", "props": {"hideInDesigner": true, "bizAlias": "cycleNameODF", "id": "cycleName", "label": "周期"}}, {"componentName": "TextField", "props": {"hideInDesigner": true, "bizAlias": "oldAmountODF", "id": "oldAmount", "label": "修改前金额"}}, {"componentName": "MoneyField", "props": {"hideInDesigner": true, "bizAlias": "newAmountODF", "id": "newAmount", "label": "修改后金额", "viewMode": false}}, {"componentName": "MoneyField", "props": {"hideInDesigner": true, "bizAlias": "changeAmountODF", "id": "changeAmount", "label": "调整金额", "viewMode": false}}], "multiple": true, "componentName": "OpenDataField", "props": {"quote": 0, "appId": "78641", "multiple": true, "label": "预算申请", "placeholder": "请选择预算规则", "id": "OpenDataField_21FJJM89EF1MO", "fields": [{"componentName": "TextField", "props": {"hideInDesigner": true, "bizAlias": "nameODF", "id": "name", "label": "预算名称"}}, {"componentName": "TextField", "props": {"hideInDesigner": true, "bizAlias": "ruleNameAllODF", "id": "ruleNameAll", "label": "预算单元"}}, {"componentName": "TextField", "props": {"hideInDesigner": true, "bizAlias": "cycleNameODF", "id": "cycleName", "label": "周期"}}, {"componentName": "TextField", "props": {"hideInDesigner": true, "bizAlias": "oldAmountODF", "id": "oldAmount", "label": "修改前金额"}}, {"componentName": "MoneyField", "props": {"hideInDesigner": true, "bizAlias": "newAmountODF", "id": "newAmount", "label": "修改后金额", "viewMode": false}}, {"componentName": "MoneyField", "props": {"hideInDesigner": true, "bizAlias": "changeAmountODF", "id": "changeAmount", "label": "调整金额", "viewMode": false}}], "openDataSource": {"type": "spi", "key": "finance_budget_apply"}, "required": false, "titleKey": "name"}}, {"reference": false, "children": [{"componentName": "TextField", "props": {"id": "contractName", "label": "合同名称"}}, {"componentName": "TextField", "props": {"id": "contractAmount", "label": "合同总额"}}, {"componentName": "TextField", "props": {"id": "contractRestAmount", "label": "待收/待付"}}, {"componentName": "TextField", "props": {"id": "contractOtherPartyList", "label": "对方主体"}}, {"componentName": "TextField", "props": {"id": "contractOurPartyList", "label": "我方主体"}}, {"componentName": "TextField", "props": {"id": "contractNo", "label": "合同编号"}}], "multiple": true, "componentName": "OpenDataField", "props": {"quote": 0, "multiple": true, "notPrint": 0, "label": "关联合同", "placeholder": "添加合同", "id": "OpenDataField_1S8NGUQR2KQ9S", "fields": [{"componentName": "TextField", "props": {"id": "contractName", "label": "合同名称"}}, {"componentName": "TextField", "props": {"id": "contractAmount", "label": "合同总额"}}, {"componentName": "TextField", "props": {"id": "contractRestAmount", "label": "待收/待付"}}, {"componentName": "TextField", "props": {"id": "contractOtherPartyList", "label": "对方主体"}}, {"componentName": "TextField", "props": {"id": "contractOurPartyList", "label": "我方主体"}}, {"componentName": "TextField", "props": {"id": "contractNo", "label": "合同编号"}}], "openDataSource": {"type": "spi", "key": "contract_open_data"}, "titleKey": "contractName"}}, {"componentName": "DepartmentField", "props": {"multiple": false, "label": "部门", "placeholder": "请选择", "id": "DepartmentField_1RVW8CQJJOUM8", "required": false}}, {"componentName": "IndustryDepartmentField", "props": {"multiple": false, "label": "行业通讯录部门", "placeholder": "请选择", "id": "IndustryDepartmentField_1WFM396CI2WOW", "required": false}}, {"componentName": "TimeAndLocationField", "props": {"label": ["当前时间", "当前地点"], "id": "TimeAndLocationField_1IKE57W9Z3LS0", "required": false}}, {"componentName": "CalculateField", "props": {"notUpper": "0", "label": "计算公式", "placeholder": "自动计算数值", "id": "CalculateField_VD6Z9Q0W524G"}}, {"componentName": "FormRelateField", "props": {"displayExtract": true, "quote": 1, "extract": false, "securityMode": 0, "label": "测试1111", "id": "FormRelateField_MC16NUEC6QKG", "title": "", "fields": [{"componentName": "TextField", "props": {"holidayOptions": [], "_extractId": "TextField_NZHLFVRUWYDC", "id": "TextField-J78F056R", "label": "单行输入框", "placeholder": "请输入", "_oriId": "TextField-J78F056R"}}, {"componentName": "MoneyField", "props": {"holidayOptions": [], "_extractId": "MoneyField_1B4GWMV54X1XC", "notUpper": "0", "id": "MoneyField-J78F0571", "label": "金额（元）大写", "placeholder": "请输入金额", "required": false, "_oriId": "MoneyField-J78F0571"}}, {"componentName": "TextareaField", "props": {"holidayOptions": [], "_extractId": "TextareaField_HS4XCSPSAZ28", "id": "TextareaField-J78F056S", "label": "多行输入框", "placeholder": "请输入", "required": false, "_oriId": "TextareaField-J78F056S"}}, {"componentName": "NumberField", "props": {"holidayOptions": [], "unit": "元", "_extractId": "NumberField_98EU8I1F9N9C", "id": "NumberField-J78F057N", "label": "数字输入框", "placeholder": "请输入", "required": false, "_oriId": "NumberField-J78F057N"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-3560876A-A313-44D2-80BE-F9601F1F8C36", "appType": 0}}, "required": false, "multi": 0, "procType": ""}}, {"componentName": "InvoiceField", "props": {"invoiceChecking": true, "appId": "78641", "label": "发票", "id": "InvoiceField_164H37604BW8W", "required": false}}, {"componentName": "RelateField", "props": {"notPrint": "1", "label": "关联审批单", "placeholder": "请选择", "id": "RelateField_140UD1G0E3YM8", "required": false}}, {"componentName": "AddressField", "props": {"needDetail": false, "label": "省市区", "id": "AddressField_SIYVYACJ6BCW", "required": false}}, {"componentName": "StarRatingField", "props": {"limit": 5, "label": "评分", "placeholder": "请输入", "id": "StarRatingField_HTRPJK3ULXC0", "required": false}}, {"componentName": "RecipientAccountField", "props": {"label": "收款账户", "placeholder": "请选择", "id": "RecipientAccountField_3EMJVRO5QSSG", "required": true}}, {"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}], "label": "流水号", "id": "SeqNumberField_1XA8AO8F8XKW0"}}, {"children": [{"componentName": "TextField", "props": {"bizAlias": "name", "label": "姓名", "placeholder": "请输入", "id": "TextField_COEWHK0NW3CW", "required": true}}, {"componentName": "IdCardField", "props": {"bizAlias": "idCardNo", "label": "身份证号码", "placeholder": "请输入", "id": "IdCardField_1KIL4I8CJPPFK", "required": true}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "男", "key": "0"}, {"value": "女", "key": "1"}], "bizAlias": "gender", "label": "性别", "placeholder": "请选择", "id": "DDSelectField_BWM7NQK0QC5C", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "nation", "label": "民族", "placeholder": "请输入", "id": "TextField_1OD8K6ZEAZA4G", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "birthdate", "label": "出生日期", "placeholder": "请选择", "id": "DDDateField_15J71EFEST1C0", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "address", "placeholder": "请输入", "label": "住址", "id": "TextField_E8L22RK7MIV4", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "issuingAuthority", "label": "签发机关", "placeholder": "请输入", "id": "TextField_8PQTMO4T6OW0", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "expirationStartDate", "label": "有效期限起始时间", "placeholder": "请选择", "id": "DDDateField_1QL5VJE4Y5YIO", "required": true}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "expirationEndDate", "label": "有效期限结束时间", "placeholder": "请选择", "id": "DDDateField_A6GM99RXUTXC", "required": true}}], "componentName": "OcrIdCardField", "props": {"options": ["name", "idCardNo", "gender", "nation", "birthdate", "address", "issuingAuthority", "expirationStartDate", "expirationEndDate"], "ocrType": "idcard", "label": "身份证识别", "placeholder": "识别出的信息会自动填充到表单", "id": "OcrIdCardField_1RWVKWHRWN400", "type": "ocr", "required": true}}, {"componentName": "OcrTextField", "props": {"ocrType": "advanced", "label": "通用文字识别", "placeholder": "请输入", "id": "OcrTextField_ZCAF3O0KUDJ4", "type": "ocr", "required": false}}]