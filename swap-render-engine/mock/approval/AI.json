[{"componentName": "DDAIField", "props": {"label": "智能控件", "id": "DDAIField-K2AD4O5B", "required": true, "rely": {"scene": "summary", "configId": "123"}}}, {"componentName": "TextField", "props": {"placeholder": "请输入", "label": "单行输入框", "id": "TextField-K2AD4O5B", "viewMode": false}}, {"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}, {"type": "suffix", "value": ""}, {"type": "field", "value": ""}], "label": "流水号", "id": "SeqNumberField_J9D4UDLZLDS0"}}, {"componentName": "TextareaField", "props": {"label": "多行输入框", "placeholder": "请输入", "id": "TextareaField_1A690U9UCI29S", "required": false}}, {"componentName": "DDSelectField", "props": {"label": "select field", "id": "SelectField-K2AD4O5B", "spread": false, "options": [{"key": "option1", "value": "选项1"}, {"key": "option2", "value": "选项1"}], "required": true}}, {"componentName": "FormRelateField", "props": {"label": "AI 数据表单", "title": "", "multi": 0, "fields": [{"aiFieldConfigModel": {"configId": "aicomponent-ada5c573-51e4-4eb5-b364-cab811c7714f", "refComponents": ["MoneyField_1NSYG7PCTO0ZK", "NumberField_1TD5BQTGLUTXC", "MoneyField_1VZOJCRH3RQ4G"], "scene": "extraction"}, "componentName": "DDAIField", "props": {"rely": {"saved": true, "configId": "aicomponent-ada5c573-51e4-4eb5-b364-cab811c7714f", "refComponents": ["MoneyField_1NSYG7PCTO0ZK", "NumberField_1TD5BQTGLUTXC", "MoneyField_1VZOJCRH3RQ4G"], "scene": "extraction"}, "notPrint": "1", "bizAlias": "", "label": "AI解读", "id": "DDAIField_18RNMWKP2TYWW", "_oriId": "DDAIField_18RNMWKP2TYWW", "_extractId": "DDAIField_1TCOOOU6PTIIO"}}, {"componentName": "DDSelectField", "props": {"rely": {}, "options": [{"value": "选项1", "key": "option_0"}, {"value": "选项2", "key": "option_1"}, {"value": "选项3", "key": "option_2"}], "bizAlias": "", "label": "单选框", "placeholder": "请选择", "id": "DDSelectField_4MI0HE267VCW", "fields": [], "dataSource": {}, "required": false, "spread": false, "ratio": 50, "behaviorLinkage": [], "_oriId": "DDSelectField_4MI0HE267VCW", "_extractId": "DDSelectField_1JFOKMW5CBVGG"}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "label": "图片", "id": "DDPhotoField_JWPPZE18TXQ8", "required": false, "maxUpload": 50, "_oriId": "DDPhotoField_JWPPZE18TXQ8", "_extractId": "DDPhotoField_5MN0NQ3FF8JK"}}, {"componentName": "DDAttachment", "props": {"label": "附件", "id": "DDAttachment_11SUDZMD70DMO", "required": false, "_oriId": "DDAttachment_11SUDZMD70DMO", "_extractId": "DDAttachment_1BBC8XKSPIUIO"}}, {"componentName": "InnerContactField", "props": {"bizAlias": "", "label": "A客户名称A", "placeholder": "请选择", "id": "InnerContactField_1ZSJKSZZRYUWW", "choice": "0", "required": false, "_oriId": "InnerContactField_1ZSJKSZZRYUWW", "_extractId": "InnerContactField_1MZ5ONUBXJ6DC"}}, {"componentName": "PhoneField", "props": {"mode": "phone", "bizAlias": "", "label": "客户联系方式", "placeholder": "请输入", "id": "PhoneField_1TF2N7ST1O1S0", "required": false, "_oriId": "PhoneField_1TF2N7ST1O1S0", "_extractId": "PhoneField_1KTVS4W4XYLTS"}}, {"componentName": "AddressField", "props": {"needDetail": 0, "rely": {"type": ""}, "bizAlias": "", "label": "客户所在地区", "id": "AddressField_S4KV9JP7L534", "fields": [], "dataSource": {}, "required": false, "_oriId": "AddressField_S4KV9JP7L534", "_extractId": "AddressField_LUA92SWA4MBK"}}, {"componentName": "MoneyField", "props": {"notUpper": "0", "bizAlias": "", "label": "原金额", "placeholder": "请输入金额", "id": "MoneyField_1NSYG7PCTO0ZK", "required": false, "_oriId": "MoneyField_1NSYG7PCTO0ZK", "_extractId": "MoneyField_1B9KU55N1E9DS"}}, {"componentName": "NumberField", "props": {"rely": {"type": ""}, "bizAlias": "", "label": "折扣比例", "placeholder": "根据地区判断", "id": "NumberField_1TD5BQTGLUTXC", "fields": [], "dataSource": {}, "required": false, "ratio": 50, "_oriId": "NumberField_1TD5BQTGLUTXC", "_extractId": "NumberField_11RZ2RUJOZDOG"}}, {"componentName": "MoneyField", "props": {"rely": {"formula": "${MoneyField_1NSYG7PCTO0ZK}*${NumberField_1TD5BQTGLUTXC}", "type": "formula", "fields": ["MoneyField_1NSYG7PCTO0ZK", "NumberField_1TD5BQTGLUTXC"], "version": 2}, "notUpper": "0", "bizAlias": "", "label": "折后金额", "placeholder": "请输入金额", "id": "MoneyField_1VZOJCRH3RQ4G", "fields": [], "dataSource": {}, "required": false, "_oriId": "MoneyField_1VZOJCRH3RQ4G", "_extractId": "MoneyField_ZGH9L8KIT8U8"}}, {"componentName": "TextareaField", "props": {"bizAlias": "", "label": "物流信息补充", "placeholder": "请输入", "id": "TextareaField_1OWY6VM16UD4W", "required": false, "_oriId": "TextareaField_1OWY6VM16UD4W", "_extractId": "TextareaField_G1FCDNRY9FCW"}}], "dataSource": {"target": {"formCode": "PROC-2113BB89-31C2-405E-B04D-8F20F186CC8A", "appUuid": "", "appType": 1, "bizType": ""}, "type": "form", "params": {"filter": ""}}, "required": false, "quote": 1, "extract": false, "displayExtract": true, "procType": "", "securityMode": 0, "id": "FormRelateField_1OMQFEOWZSBGG"}}, {"children": [{"componentName": "TextField", "props": {"label": "单行输入框", "placeholder": "请输入", "id": "TextField_DNKUQYMKUI2O", "required": false, "ratio": 50}}], "componentName": "TableField", "props": {"tableViewMode": "table", "label": "表格", "id": "TableField_1EMRKW5WU55A8", "actionName": "添加"}}]