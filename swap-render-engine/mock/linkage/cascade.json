[{"componentName": "CascadeField", "props": {"bizAlias": "product_type", "id": "CascadeField_1TII0OPU163K0", "label": "产品分类", "placeholder": "请选择", "noticeContent": {"noticeDesc": "可在crm中设置产品分类", "noticeBtnText": "去设置", "jumpLinkUrl": "https://baidu.com"}, "showSearch": true, "options": [{"parentId": 0, "fullNamePath": "分类2", "fullIdPath": "813001", "code": "123456888", "level": 0, "name": "分类2", "id": 813001, "attributes": {"remark": "测试备注"}, "children": [{"parentId": 813001, "fullNamePath": "分类2/第二个等级", "fullIdPath": "813001/937050", "code": "234234", "level": 1, "name": "第二个等级", "id": 937050, "children": [{"parentId": 937050, "fullNamePath": "分类2/第二个等级/三级", "fullIdPath": "813001/937050/12333", "code": "12333", "level": 2, "name": "三级", "id": 12333, "children": [{"parentId": 12333, "fullNamePath": "分类2/第二个等级/三级/四级的测试", "fullIdPath": "813001/937050/12333/44444", "code": "44444", "level": 3, "name": "四级的测试", "id": 44444, "children": [{"parentId": 44444, "fullNamePath": "分类2/第二个等级/三级/四级的测试/五级的一些测试文案", "fullIdPath": "813001/937050/12333/44444/55555", "code": "55555", "level": 4, "name": "五级的一些测试文案", "id": 55555, "attributes": {"remark": "测试备注"}}]}]}]}]}, {"parentId": 0, "fullNamePath": "分类3", "fullIdPath": "937009", "code": "456789", "level": 0, "name": "分类3", "id": 937009, "children": [{"parentId": 937009, "fullNamePath": "分类3/ttt", "fullIdPath": "937009/1055015", "code": "13579", "level": 1, "name": "ttt", "id": 1055015, "children": [{"parentId": 1055015, "fullNamePath": "分类3/ttt/tdt", "fullIdPath": "937009/1055015/1055019", "code": "13579", "level": 2, "name": "tdt", "id": 1055019, "children": [{"parentId": 1055019, "fullNamePath": "分类3/ttt/tdt/tff", "fullIdPath": "937009/1055015/1055019/1055020", "code": "13579", "level": 3, "name": "tff", "id": 1055020}]}]}]}, {"parentId": 0, "fullNamePath": "分类4", "fullIdPath": "1055037", "code": "1055037", "level": 0, "name": "分类4", "id": 1055037}, {"parentId": 0, "fullNamePath": "分类4", "fullIdPath": "1055039", "code": "1055039", "level": 0, "name": "分类4", "id": 1055039}, {"parentId": 0, "fullNamePath": "分类4", "fullIdPath": "1058837", "code": "1058837", "level": 0, "name": "分类4", "id": 1058837}, {"parentId": 0, "fullNamePath": "分类4", "fullIdPath": "1075037", "code": "1075037", "level": 0, "name": "分类4", "id": 1075037}], "addBtnLink": true, "required": false}}, {"componentName": "DDDateRangeField", "props": {"duration": false, "unit": "天", "visible": true, "format": "yyyy-MM-dd", "bizAlias": "actual_date", "label": ["实际开始时间", "实际结束时间"], "placeholder": "请选择", "id": "DDDateRangeField_13ULKFXHPVK00", "durationLabel": "时长", "required": false}}, {"componentName": "DDDateRangeField", "props": {"duration": false, "unit": "天", "visible": true, "format": "yyyy-MM-dd", "bizAlias": "actual_date", "label": ["实际开始时间", "实际结束时间"], "placeholder": "请选择", "id": "DDDateRangeField_13ULKFXHPVK00", "durationLabel": "时长", "required": false}}, {"componentName": "TableField", "props": {"tableViewMode": "table", "bizAlias": "addDetail", "label": "明细", "id": "TableField_1SXCSL2WNYYO0", "actionName": "添加"}, "children": [{"componentName": "NumberField", "props": {"bizAlias": "", "label": "数量", "placeholder": "请输入数字", "id": "NumberField_1HO3RY7LJJ9C0", "required": true, "ratio": 50}}, {"componentName": "MoneyField", "props": {"notUpper": "1", "bizAlias": "", "label": "单价（元）", "placeholder": "请输入金额", "id": "MoneyField_1TASUCN4UEV40", "required": true}}, {"componentName": "CalculateField", "props": {"notUpper": "1", "formula": [{"id": "NumberField_1HO3RY7LJJ9C0"}, "*", {"id": "MoneyField_1TASUCN4UEV40"}], "bizAlias": "", "label": "小计", "placeholder": "自动计算数值", "id": "CalculateField_XY69G90XYXC0"}}]}]