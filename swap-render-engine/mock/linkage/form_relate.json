[{"componentName": "FormRelateField", "props": {"quote": 1, "bizAlias": "", "label": "客户", "id": "FormRelateField_N6XJ5C8FDHS0", "title": "", "fields": [{"componentName": "TextField", "props": {"bizAlias": "customer_name", "placeholder": "请输入组织全称", "label": "客户名称", "id": "TextField-K2U5DHAA", "required": true}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "SWAPP-4FA3D2B5550ABD020DFD301B0FE9F765", "bizType": "crm_customer", "formCode": "PROC-8FBA97EC-3607-48DD-888B-B8C71778C080", "appType": 1}}, "required": true, "multi": 0}}, {"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "bizAlias": "", "label": "申请出库日期", "placeholder": "请选择", "id": "DDDateField_IJBK4SKI8XK0", "required": true}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "天津中环恒通科技有限公司", "key": "option_1"}], "bizAlias": "", "label": "出库单位", "placeholder": "请选择", "id": "DDSelectField_1A9YMYUPQ55S0", "required": true, "spread": false, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "FormRelateField", "props": {"quote": 1, "bizAlias": "", "label": "合同签订审批", "id": "FormRelateField_6QKHR9H7MOO0", "title": "", "fields": [{"componentName": "DDDateField", "props": {"unit": "天", "format": "yyyy-MM-dd", "placeholder": "请选择", "label": "签约日期", "id": "DDDateField-IH4TJ8FA", "required": true}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "天津盛奥华环保科技有限公司", "key": "option_0"}, {"value": "天津中环恒通科技有限公司", "key": "option_1"}, {"value": "北京贝翌环保科技有限公司", "key": "option_2"}, {"extension": {"image": ""}, "value": "鑫巨源（天津）科技有限公司", "key": "option_KP3OFH24ZA80"}], "bizAlias": "", "label": "我方单位名称", "placeholder": "请选择", "id": "DDSelectField_1NE9SNR4B7B40", "required": true, "spread": false, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "盛奥华业务-销售合同", "key": "option_0"}, {"extension": {"image": ""}, "value": "中环恒通业务-销售合同", "key": "option_MHA4WUWUU9C0"}, {"value": "采购合同", "key": "option_1"}, {"value": "合作协议", "key": "option_2"}, {"value": "其他合同", "key": "option_JZ2MZCEO"}], "bizAlias": "", "placeholder": "请选择", "label": "合同类型", "id": "DDSelectField-JZ2MGG0R", "required": true, "behaviorLinkage": []}}, {"children": [{"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "", "label": "项目及物品名称", "id": "FormRelateField_9MW3V2IESAO0", "title": "", "fields": [{"componentName": "TextField", "props": {"bizAlias": "", "label": "产品名称", "placeholder": "请输入", "id": "TextField_1VMRORO3RPFK0", "required": true, "ratio": 50}}, {"componentName": "TextField", "props": {"bizAlias": "", "label": "规格型号", "placeholder": "请输入", "id": "TextField_HOQXFHH4WIO0", "required": true, "ratio": 50}}, {"componentName": "TextField", "props": {"bizAlias": "", "label": "产品厂家", "placeholder": "请输入", "id": "TextField_9MHQC3QHBM00", "required": true, "ratio": 50}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "套", "key": "option_0"}, {"value": "瓶", "key": "option_1"}, {"value": "个", "key": "option_2"}, {"extension": {"image": ""}, "value": "卷", "key": "option_14QK7UUA9OF40"}, {"extension": {"image": ""}, "value": "支", "key": "option_1L150753M6QO0"}, {"extension": {"image": ""}, "value": "台", "key": "option_PN02MDZZWZ40"}, {"extension": {"image": ""}, "value": "根", "key": "option_172O31Y20S3G0"}], "bizAlias": "", "label": "计量单位", "placeholder": "请选择", "id": "DDSelectField_1LMIAYZLLC9S0", "required": true, "spread": false, "ratio": 50, "behaviorLinkage": []}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "SWAPP-4FA3D2B5550ABD020DFD301B0FE9F765", "bizType": "", "formCode": "PROC-602662A5-00A5-47C8-A67C-951129E1E065", "appType": 1}}, "required": true, "multi": 0}}, {"componentName": "NumberField", "props": {"bizAlias": "", "label": "数量", "placeholder": "请输入数字", "id": "NumberField_1HO3RY7LJJ9C0", "required": true, "ratio": 50}}, {"componentName": "MoneyField", "props": {"notUpper": "1", "bizAlias": "", "label": "单价（元）", "placeholder": "请输入金额", "id": "MoneyField_1TASUCN4UEV40", "required": true}}, {"componentName": "CalculateField", "props": {"notUpper": "1", "formula": [{"id": "NumberField_1HO3RY7LJJ9C0"}, "*", {"id": "MoneyField_1TASUCN4UEV40"}], "bizAlias": "", "label": "小计", "placeholder": "自动计算数值", "id": "CalculateField_XY69G90XYXC0"}}], "componentName": "TableField", "props": {"tableViewMode": "table", "bizAlias": "", "label": "合同签订明细", "id": "TableField_1SXCSL2WNYYO0", "actionName": "添加"}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "顺丰快递", "key": "option_0"}, {"value": "公司送货", "key": "option_1"}, {"value": "客户自取", "key": "option_2"}, {"extension": {"image": ""}, "value": "其他", "key": "option_1OKKXOS2VEYO0"}], "bizAlias": "", "label": "发货方式", "placeholder": "请选择", "id": "DDSelectField_1U3ZUIU6TG0W0", "required": true, "spread": false, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "DDPhotoField", "props": {"label": "图片", "id": "DDPhotoField-IH4TJ8FJ"}}, {"componentName": "DDAttachment", "props": {"bizAlias": "", "label": "附件", "id": "附件", "required": false}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-97300E75-19AC-4A5E-9AC7-4CAAE89722E8", "appType": 0}}, "required": true, "multi": 0}}, {"componentName": "FormRelateField", "props": {"quote": 1, "bizAlias": "", "label": "联系人", "id": "FormRelateField_118WAFABBL5S0", "title": "", "fields": [{"componentName": "TextField", "props": {"bizAlias": "contact_name", "placeholder": "请输入", "label": "姓名", "id": "TextField-K2U5O2WI", "required": true}}, {"componentName": "PhoneField", "props": {"mode": "phone_tel", "bizAlias": "contact_phone", "placeholder": "请输入", "label": "手机号", "id": "TextField-K2U5O2WJ", "required": true}}, {"componentName": "TextareaField", "props": {"bizAlias": "", "label": "收货地址", "placeholder": "请输入", "id": "TextareaField_18KWFG4KAHNK0", "required": false}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "SWAPP-4FA3D2B5550ABD020DFD301B0FE9F765", "bizType": "crm_contact", "formCode": "PROC-44835493-0C5B-4AE2-AD74-5FDE5BA78E86", "appType": 1}}, "required": true, "multi": 0}}, {"componentName": "TextareaField", "props": {"placeholder": "请输入", "label": "备注", "id": "TextareaField-K02895PA"}}]