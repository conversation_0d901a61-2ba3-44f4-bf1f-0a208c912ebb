[{"children": [{"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}], "bizAlias": "stock_no", "label": "编号", "id": "SeqNumberField_1C1FRHLDQV28"}}, {"componentName": "FormRelateField", "props": {"quote": 1, "bizAlias": "order_sell", "label": "销售订单", "id": "FormRelateField_1ER1A50TAYKG0", "title": "", "fields": [{"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}], "bizAlias": "order_no", "label": "订单编号", "id": "SeqNumberField_1Y0WILAE4PB40"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-B9D15BB6-E949-41E8-B9A7-CB7C00A5FE19", "appType": 0}}, "required": false, "multi": 0}}, {"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "task_order", "label": "生产任务单", "id": "FormRelateField_22H58689H3TS0", "title": "", "fields": [{"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}], "bizAlias": "task_order_no", "label": "生产单号", "id": "SeqNumberField_1TUA06RKDCIO0"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "dingtalk.palmmanufacture.task_order", "formCode": "PROC-F2FB9F1B-B4E8-4488-866D-C5CCDC5E880F", "appType": 1}}, "required": false, "multi": 0}}, {"children": [{"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "goods", "label": "物料", "id": "FormRelateField_Q5RTXBOO3CG0", "title": "", "fields": [{"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}], "bizAlias": "material_no", "label": "物料编号", "id": "SeqNumberField_NSBF6SAX2SG0"}}, {"componentName": "TextField", "props": {"bizAlias": "material_name", "label": "名称", "placeholder": "请输入", "id": "TextField_K8BWAVZ9V0W0", "required": true, "ratio": 50}}, {"componentName": "DDSelectField", "props": {"rely": {}, "options": [{"value": "个", "key": "option_0"}, {"value": "千克", "key": "option_1"}, {"value": "箱", "key": "option_2"}, {"extension": {"image": ""}, "value": "盒", "key": "option_MKY6CYLIRVK0"}], "bizAlias": "unit", "label": "计量单位", "placeholder": "请选择", "id": "DDSelectField_4WMFE6MF7EE0", "fields": [], "dataSource": {}, "required": false, "spread": false, "ratio": 50, "behaviorLinkage": []}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "dingtalk.palmmanufacture.material", "formCode": "PROC-12B90453-6E33-486D-B776-CE2328BB9705", "appType": 1}}, "required": true, "multi": 0}}, {"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "storage", "label": "库位", "id": "FormRelateField_KPPQFXRBXOW0", "title": "", "fields": [{"componentName": "TextField", "props": {"bizAlias": "storage_no", "label": "库位号", "placeholder": "请输入", "id": "TextField_1TXF1IBLYNA80", "required": true, "ratio": 50}}, {"componentName": "TextField", "props": {"visible": false, "rely": {"formula": "", "type": "rely", "fields": ["FormRelateField_779IMP9A2A00"], "behavior": "readonly"}, "defaultValue": "", "bizAlias": "warehouse_no", "label": "仓库编号(隐藏)", "placeholder": "请输入", "id": "TextField_BF079TH8ZZS0", "fields": [{"props": {"id": "SeqNumberField_1G5SRUJP40YO0"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "FormRelateField_779IMP9A2A00", "fieldId": "id"}]}, "target": {"appUuid": "ding4f7815e99eeac513ffe93478753d9884", "formCode": "PROC-19511D9E-84B3-446D-B5EA-A07BCBF999A4"}}, "required": false, "ratio": 50}}, {"componentName": "TextField", "props": {"rely": {"formula": "", "type": "rely", "fields": ["FormRelateField_779IMP9A2A00"], "behavior": "readonly"}, "defaultValue": "", "bizAlias": "warehouse_name", "label": "仓库名称(隐藏)", "placeholder": "请输入", "id": "TextField_1OFBHODP2XDS0", "fields": [{"props": {"id": "TextField_DE4UUJH55R40"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "FormRelateField_779IMP9A2A00", "fieldId": "id"}]}, "target": {"appUuid": "ding4f7815e99eeac513ffe93478753d9884", "formCode": "PROC-19511D9E-84B3-446D-B5EA-A07BCBF999A4"}}, "required": false, "ratio": 50}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "dingtalk.wms.storage", "formCode": "PROC-2592CC65-35F8-4838-BF2A-83245091BBCC", "appType": 1}}, "required": true, "multi": 0}}, {"componentName": "NumberField", "props": {"bizAlias": "actual_receive_count", "label": "入库数量", "placeholder": "请输入数字", "id": "NumberField_1R6RG1368D280", "required": true, "ratio": 50}}], "componentName": "TableField", "props": {"tableViewMode": "table", "bizAlias": "stock_detail", "label": "入库详情", "id": "TableField_1NENDTT9QMU80", "actionName": "添加"}}, {"componentName": "DDDateField", "props": {"unit": "天", "rely": {"formula": "TODAY()", "type": "formula", "fields": [], "version": 2}, "defaultValue": "", "format": "yyyy-MM-dd", "bizAlias": "biz_time", "label": "入库时间", "placeholder": "请选择", "id": "DDDateField_NBGOPZZ3IE80", "fields": [], "dataSource": {}, "required": true}}], "componentName": "DDBizSuite", "props": {"bizType": "dingtalk.wms.stock_in_produce", "extract": true, "isThirdSuite": true, "bizAlias": "dingtalk.wms.stock_in_produce", "id": "DDBizSuite_1GPW6SG6JKPS0"}}]