[{"componentName": "TextField", "props": {"placeholder": "请输入", "label": "单行输入框", "id": "TextField-K2AD4O5B"}}, {"componentName": "FormRelateField", "props": {"quote": 1, "extract": true, "displayExtract": true, "bizAlias": "", "label": "物品领用", "id": "FormRelateField_RYPRDSYR4HC0", "title": "", "fields": [{"componentName": "TextField", "props": {"label": "物品用途", "placeholder": "如：日常办公", "id": "物品用途", "required": true}}, {"componentName": "IdCardField", "props": {"label": "身份证", "placeholder": "请输入", "id": "IdCardField_1DNDR0KQW0680", "required": false}}, {"componentName": "TextareaField", "props": {"placeholder": "请输入物品领用详情说明", "label": "领用详情", "id": "领用详情", "required": false}}, {"componentName": "DDPhotoField", "props": {"label": "图片", "id": "图片"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-BC1A1D61-FA93-4CEC-B551-6C91AC4E19B3", "appType": 0}}, "required": false, "multi": 0}}, {"children": [{"componentName": "TextField", "props": {"placeholder": "请输入", "label": "单行输入框", "id": "TextField-K2AD4O5Bcc"}}, {"componentName": "FormRelateField", "props": {"quote": 0, "extract": true, "displayExtract": true, "bizAlias": "", "label": "物品领用", "id": "FormRelateField_85NWDKR71440", "title": "", "fields": [{"componentName": "TextField", "props": {"label": "物品用途", "placeholder": "如：日常办公", "id": "物品用途", "required": true}}, {"componentName": "IdCardField", "props": {"label": "身份证", "placeholder": "请输入", "id": "IdCardField_1DNDR0KQW0680", "required": false}}, {"componentName": "TextareaField", "props": {"placeholder": "请输入物品领用详情说明", "label": "领用详情", "id": "领用详情", "required": false}}, {"componentName": "DDPhotoField", "props": {"label": "图片", "id": "图片"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-BC1A1D61-FA93-4CEC-B551-6C91AC4E19B3", "appType": 0}}, "required": false, "multi": 0}}], "componentName": "TableField", "props": {"tableViewMode": "table", "bizAlias": "", "label": "表格", "id": "TableField_C2ISN2MH29C0", "actionName": "添加"}}, {"children": [{"componentName": "TextField", "props": {"placeholder": "请输入", "label": "单行输入框", "id": "TextField-K2AD4O5B1"}}, {"componentName": "FormRelateField", "props": {"quote": 0, "extract": true, "displayExtract": true, "bizAlias": "", "label": "物品领用", "id": "FormRelateField_117091A04I740", "title": "", "fields": [{"componentName": "TextField", "props": {"label": "物品用途", "placeholder": "如：日常办公", "id": "物品用途", "required": true}}, {"componentName": "IdCardField", "props": {"label": "身份证", "placeholder": "请输入", "id": "IdCardField_1DNDR0KQW0680", "required": false}}, {"componentName": "TextareaField", "props": {"placeholder": "请输入物品领用详情说明", "label": "领用详情", "id": "领用详情", "required": false}}, {"componentName": "DDPhotoField", "props": {"label": "图片", "id": "图片"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-BC1A1D61-FA93-4CEC-B551-6C91AC4E19B3", "appType": 0}}, "required": false, "multi": 0}}, {"componentName": "TextField", "props": {"placeholder": "请输入", "label": "单行输入框", "id": "TextField-K2AD4O5B2"}}], "componentName": "TableField", "props": {"tableViewMode": "list", "bizAlias": "", "label": "表格", "id": "TableField_14EA44X3PRJ40", "actionName": "添加"}}]