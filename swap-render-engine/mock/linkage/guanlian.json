[{"componentName": "DDSelectField", "props": {"options": [{"value": "外出报销补贴", "key": "option_3EVBB7VM3GW0"}, {"value": "用车报销补贴", "key": "option_AMNPY21Y8JCW"}, {"value": "团建费用报销", "key": "option_1"}, {"value": "采购费用报销", "key": "option_JXCLUJF9"}, {"value": "维修费用报销", "key": "option_JXCLUJFA"}, {"value": "代偿款项报销", "key": "option_1ZO2E30P11A80"}, {"value": "对外接待报销", "key": "option_HGH68RYB7UO0"}, {"value": "非因公消费补贴", "key": "option_JZWENEG5"}, {"value": "加班餐费补贴", "key": "option_K0BZO4NG"}, {"value": "工作服补贴", "key": "option_KEJKU3H5"}, {"value": "其他", "key": "option_JXJT7MVN"}], "bizAlias": "", "placeholder": "请选择", "label": "费用类别", "id": "DDSelectField-JXCLU0WV", "required": true, "behaviorLinkage": [{"value": "option_KEJKU3H5", "targets": [{"behavior": "NORMAL", "fieldId": "MoneyField_1A0809XWS7XC0"}, {"behavior": "NORMAL", "fieldId": "FormRelateField_11CZ74EVDHZ40"}]}, {"value": "option_AMNPY21Y8JCW", "targets": [{"behavior": "NORMAL", "fieldId": "DDSelectField_1QNJ57B2F8MPS"}]}, {"value": "option_3EVBB7VM3GW0", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_1NHEXIF58TP1C"}]}, {"value": "option_JXCLUJF9", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_7HQJAT8LO840"}]}, {"value": "option_JXCLUJFA", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_7HQJAT8LO840"}]}, {"value": "option_1ZO2E30P11A80", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_7HQJAT8LO840"}]}, {"value": "option_1", "targets": [{"behavior": "NORMAL", "fieldId": "DDSelectField_ECV5I0M1EFS0"}]}, {"value": "option_HGH68RYB7UO0", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_7HQJAT8LO840"}]}, {"value": "option_K0BZO4NG", "targets": [{"behavior": "NORMAL", "fieldId": "MoneyField_1A0809XWS7XC0"}]}, {"value": "option_JXJT7MVN", "targets": [{"behavior": "NORMAL", "fieldId": "MoneyField_1A0809XWS7XC0"}]}, {"value": "option_JZWENEG5", "targets": [{"behavior": "NORMAL", "fieldId": "TextNote_XYDQUJJ538G0"}, {"behavior": "NORMAL", "fieldId": "MoneyField_1M0IQHO2N8HS0"}, {"behavior": "NORMAL", "fieldId": "MoneyField_23QFEAMO5WN40"}]}]}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "使用非奖励经费", "key": "option_0"}, {"value": "仅使用奖励经费", "key": "option_1"}], "bizAlias": "", "label": "使用团建经费类别", "placeholder": "请选择", "id": "DDSelectField_ECV5I0M1EFS0", "required": true, "spread": false, "ratio": 50, "behaviorLinkage": [{"value": "option_1", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_1UMCJHHXQ0W00"}]}, {"value": "option_0", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_7HQJAT8LO840"}]}]}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "用车里程及附带费用报销补贴", "key": "option_1BJ80PW5WQGW0"}, {"value": "停车月租费补贴", "key": "option_21D7Q25L3BB4"}, {"value": "汽车保养费补贴", "key": "option_BKZFZUKJXQ00"}, {"value": "商业第三者责任保险费补贴", "key": "option_X9AFQDCGDSW0"}], "bizAlias": "", "label": "用车报销补贴类别", "placeholder": "请选择", "id": "DDSelectField_1QNJ57B2F8MPS", "required": true, "ratio": 50, "behaviorLinkage": [{"value": "option_BKZFZUKJXQ00", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_13GZ7TELP4OW0"}, {"behavior": "NORMAL", "fieldId": "MoneyField_1A0809XWS7XC0"}]}, {"value": "option_21D7Q25L3BB4", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_1E242SN33SAO0"}, {"behavior": "NORMAL", "fieldId": "MoneyField_1A0809XWS7XC0"}]}, {"value": "option_X9AFQDCGDSW0", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_NFK260J2CGG"}, {"behavior": "NORMAL", "fieldId": "MoneyField_1A0809XWS7XC0"}]}]}}, {"componentName": "DepartmentField", "props": {"multiple": true, "bizAlias": "", "label": "费用归属部门", "id": "DepartmentField_K91JLTOK", "required": true}}, {"children": [{"componentName": "MoneyField", "props": {"payEnable": false, "notUpper": "0", "label": "金额（元）", "placeholder": "请输入金额", "id": "MoneyField_1HOADFKIONUV4", "required": true}}, {"componentName": "FormRelateField", "props": {"quote": 0, "rely": {"formula": "", "fields": [], "type": "rely"}, "bizAlias": "", "label": "外出", "id": "FormRelateField_HLGVBEFY9P8G", "title": "", "fields": [{"componentName": "InnerContactField", "props": {"label": "内部互动同事", "placeholder": "请选择", "id": "InnerContactField_20IMMLKQXN7R4", "choice": "0", "required": true}}, {"componentName": "ExternalContactField", "props": {"placeholder": "请选择", "label": "外出联系人", "id": "ExternalContactField-K2ZWADS8", "required": true}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "同行员工驾车", "key": "option_1"}, {"value": "打车", "key": "option_0"}, {"value": "同行员工打车", "key": "option_KEN1NMWE"}, {"value": "客车、火车等公共交通", "key": "option_1NWX4GAA63GG0"}, {"value": "其他", "key": "option_1MVUQLWTNS9VK"}], "bizAlias": "", "label": "外出方式", "placeholder": "请选择", "id": "DDSelectField_XYTHZKORLTZ4", "required": true, "behaviorLinkage": [{"value": "option_1", "targets": [{"behavior": "NORMAL", "fieldId": "InnerContactField_2SLBP26D7VWG"}, {"behavior": "NORMAL", "fieldId": "TimeAndLocationField_H6Z0DUB2N85C"}]}, {"value": "option_0", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_15YY6UUR65C00"}, {"behavior": "NORMAL", "fieldId": "DDPhotoField_1UNOOUJ6N5TS0"}, {"behavior": "NORMAL", "fieldId": "TextNote_1ALNKKT6KK800"}]}, {"value": "option_1MVUQLWTNS9VK", "targets": [{"behavior": "NORMAL", "fieldId": "TimeAndLocationField_H6Z0DUB2N85C"}]}, {"value": "option_KEN1NMWE", "targets": [{"behavior": "NORMAL", "fieldId": "TimeAndLocationField_H6Z0DUB2N85C"}]}]}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "bizAlias": "", "label": "计划行程截图", "id": "DDPhotoField_1UNOOUJ6N5TS0", "required": true}}, {"componentName": "InnerContactField", "props": {"placeholder": "请选择", "label": "同行员工", "id": "InnerContactField-K49GJJ9N", "choice": "1"}}, {"componentName": "TextareaField", "props": {"placeholder": "请输入", "label": "外出地点及办理事项", "id": "TextareaField_K85HMY38", "required": true}}, {"children": [{"componentName": "DDPhotoField", "props": {"watermark": false, "bizAlias": "", "label": "请在行程中上传打车截图", "id": "DDPhotoField_SDI5L0NLI0W0", "required": true}}], "componentName": "TableField", "props": {"tableViewMode": "table", "bizAlias": "", "label": "打车行程截图明细", "id": "TableField_15YY6UUR65C00", "actionName": "如需多次打车，请点击添加"}}], "dataSource": {"type": "form", "params": {"filter": "", "filters": [{"valueType": "fixed", "filterType": "EQ", "value": "审批通过", "fieldId": "proc_out_result"}]}, "target": {"appUuid": "", "formCode": "PROC-BB385C5F-64C4-4AA0-92EC-EFCC1EA84D0A"}}, "required": true, "multi": 0}}, {"componentName": "DDAttachment", "props": {"label": "附件", "id": "DDAttachment_1ZJW4USQY8GLC", "required": false}}, {"componentName": "TextField", "props": {"bizAlias": "", "label": "备注", "placeholder": "请输入", "id": "TextField_1IQEDCP4N5NK0", "required": false, "ratio": 50}}], "componentName": "TableField", "props": {"tableViewMode": "table", "statField": [{"payEnable": false, "notUpper": "0", "label": "金额（元）", "placeholder": "请输入金额", "id": "MoneyField_1HOADFKIONUV4", "required": true}], "bizAlias": "", "label": "外出费用报销补贴明细", "id": "TableField_1NHEXIF58TP1C", "actionName": "添加费用明细"}}, {"children": [{"componentName": "MoneyField", "props": {"payEnable": false, "notUpper": "0", "bizAlias": "", "label": "油费补贴金额", "placeholder": "请输入金额", "id": "MoneyField_1ZRFSP01U40ZK", "required": true}}, {"componentName": "MoneyField", "props": {"payEnable": false, "notUpper": "0", "bizAlias": "", "label": "用车附带费用报销补贴金额", "placeholder": "请输入金额", "id": "MoneyField_1UXAARCJHAYO0", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "", "label": "用车附带费用详情", "placeholder": "请输入", "id": "TextField_15OVG648L1TS0", "required": false, "ratio": 50}}, {"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "", "label": "用车申请", "id": "FormRelateField_1PY3MK16RKZK0", "title": "", "fields": [{"componentName": "DDSelectField", "props": {"options": [{"value": "外访催收", "key": "option_0"}, {"value": "国家机关", "key": "option_K2WVMARW"}, {"value": "债务人", "key": "option_K1HMPRV3"}, {"value": "客户", "key": "option_K1HMPRV2"}, {"value": "供应商", "key": "option_K3822UZ6"}, {"value": "合作渠道", "key": "option_K382316I"}, {"value": "车主、业主", "key": "option_K38GK5OO"}, {"value": "内部互动", "key": "option_1K91DW91DSI68"}, {"value": "其他", "key": "option_K1HMPRV6"}], "bizAlias": "", "placeholder": "请选择", "label": "用车事由", "id": "DDSelectField-K1HMC3ZS", "required": true, "behaviorLinkage": [{"value": "option_K1HMPRV2", "targets": [{"behavior": "NORMAL", "fieldId": "ExternalContactField-K1HMC3ZT"}]}, {"value": "option_K1HMPRV3", "targets": [{"behavior": "NORMAL", "fieldId": "ExternalContactField-K1HMC3ZT"}]}, {"value": "option_K1HMPRV6", "targets": [{"behavior": "NORMAL", "fieldId": "ExternalContactField-K1HMC3ZT"}]}, {"value": "option_K2WVMARW", "targets": [{"behavior": "NORMAL", "fieldId": "ExternalContactField-K1HMC3ZT"}]}, {"value": "option_K3822UZ6", "targets": [{"behavior": "NORMAL", "fieldId": "ExternalContactField-K1HMC3ZT"}]}, {"value": "option_K382316I", "targets": [{"behavior": "NORMAL", "fieldId": "ExternalContactField-K1HMC3ZT"}]}, {"value": "option_K38GK5OO", "targets": [{"behavior": "NORMAL", "fieldId": "ExternalContactField-K1HMC3ZT"}]}, {"value": "option_1K91DW91DSI68", "targets": [{"behavior": "NORMAL", "fieldId": "InnerContactField_1CWARCHNHS3Y8"}]}, {"value": "option_0", "targets": [{"behavior": "NORMAL", "fieldId": "DDPhotoField_SIYZGGBU6DC0"}]}]}}, {"componentName": "InnerContactField", "props": {"label": "内部互动同事", "placeholder": "请选择", "id": "InnerContactField_1CWARCHNHS3Y8", "choice": "0", "required": true}}, {"componentName": "ExternalContactField", "props": {"placeholder": "请选择", "label": "外出联系人", "id": "ExternalContactField-K1HMC3ZT", "required": true}}, {"componentName": "InnerContactField", "props": {"placeholder": "请选择", "label": "同行员工", "id": "InnerContactField-K1SVD8GM", "choice": "1"}}, {"componentName": "TextareaField", "props": {"placeholder": "请输入", "label": "外出地点及办理事项", "id": "TextareaField-JN9P42P7", "required": true}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "bizAlias": "", "label": "请上传计划行程导航截图", "id": "DDPhotoField_SIYZGGBU6DC0", "required": true}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "粤YTZ038", "key": "option_0"}, {"value": "粤QDP029", "key": "option_1"}, {"value": "其他", "key": "option_2"}], "placeholder": "请选择需要使用的车辆号牌", "label": "车辆号牌", "id": "DDSelectField-JZDV6Z10", "required": true, "behaviorLinkage": [{"value": "option_2", "targets": [{"behavior": "NORMAL", "fieldId": "TextField-JZDV6Z11"}]}]}}, {"componentName": "TextField", "props": {"placeholder": "其他车辆号牌，需手动输入", "label": "车辆号牌（其他）", "id": "TextField-JZDV6Z11", "required": true}}, {"componentName": "NumberField", "props": {"unit": "km", "placeholder": "请填写出车时里程表公里数", "label": "出车里程", "id": "NumberField-JO7YXNES", "required": true}}, {"componentName": "DDPhotoField", "props": {"watermark": true, "label": "出车里程表照片", "id": "DDPhotoField-IH4UNLKN", "required": true}}, {"componentName": "NumberField", "props": {"unit": "km", "placeholder": "请填写到达或返车时里程表公里数", "label": "到达或返车里程", "id": "NumberField-JO7YXNET", "required": true}}, {"componentName": "DDPhotoField", "props": {"watermark": true, "label": "到达或返车里程表照片", "id": "DDPhotoField-JO7YXNEU", "required": true}}, {"componentName": "CalculateField", "props": {"payEnable": false, "notUpper": "1", "formula": [{"id": "NumberField-JO7YXNET"}, "-", {"id": "NumberField-JO7YXNES"}], "bizAlias": "", "placeholder": "自动计算数值", "label": "用车里程", "id": "CalculateField-JO7YXNER", "required": false}}, {"componentName": "DDAttachment", "props": {"label": "附件", "id": "DDAttachment-JO7ZZ6V4"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-798D74F2-**************-DC238F2F57FF", "appType": 0}}, "required": false, "multi": 0}}, {"componentName": "DDAttachment", "props": {"bizAlias": "", "label": "用车附带费用凭证", "id": "DDAttachment_1YGDQ2LD0UQO0", "required": false}}, {"componentName": "TextField", "props": {"bizAlias": "", "label": "备注", "placeholder": "请输入", "id": "TextField_1IT1OMJQKZUO0", "required": false, "ratio": 50}}], "componentName": "TableField", "props": {"tableViewMode": "table", "statField": [{"payEnable": false, "notUpper": "0", "label": "油费补贴金额", "placeholder": "请输入金额", "id": "MoneyField_1ZRFSP01U40ZK", "required": true}, {"payEnable": false, "upper": true, "id": "MoneyField_1UXAARCJHAYO0", "label": "用车附带费用报销补贴金额"}], "bizAlias": "", "label": "用车里程及附带费用报销补贴明细", "id": "TableField_1MZFX9DVMW0E8", "actionName": "添加费用明细"}}, {"componentName": "MoneyField", "props": {"notUpper": "1", "bizAlias": "", "label": "非因公消费金额（元）", "placeholder": "请输入金额", "id": "MoneyField_1M0IQHO2N8HS0", "required": true}}, {"componentName": "MoneyField", "props": {"notUpper": "1", "bizAlias": "", "label": "补贴金额（元）", "placeholder": "请输入金额", "id": "MoneyField_23QFEAMO5WN40", "required": true}}, {"componentName": "TextNote", "props": {"notPrint": "0", "bizAlias": "", "id": "TextNote_XYDQUJJ538G0", "content": "非因公消费补贴合并于员工工资发放，不计入生成支付单的总金额。"}}, {"componentName": "MoneyField", "props": {"notUpper": "0", "bizAlias": "", "label": "报销补贴金额（元）", "placeholder": "请输入金额", "id": "MoneyField_1A0809XWS7XC0", "required": true}}, {"children": [{"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "", "label": "用车申请", "id": "FormRelateField_1K9269JJ295S0", "title": "", "fields": [{"componentName": "TextField", "props": {"placeholder": "其他车辆号牌，需手动输入", "label": "车辆号牌（其他）", "id": "TextField-JZDV6Z11", "required": true}}, {"componentName": "DDPhotoField", "props": {"watermark": true, "label": "出车里程表照片", "id": "DDPhotoField-IH4UNLKN", "required": true}}, {"componentName": "DDPhotoField", "props": {"watermark": true, "label": "到达或返车里程表照片", "id": "DDPhotoField-JO7YXNEU", "required": true}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-798D74F2-**************-DC238F2F57FF", "appType": 0}}, "required": true, "multi": 0}}], "componentName": "TableField", "props": {"tableViewMode": "table", "bizAlias": "", "label": "私车公用次数累计已达补贴标准的用车审批单明细", "id": "TableField_1E242SN33SAO0", "actionName": "添加用车审批单"}}, {"children": [{"componentName": "FormRelateField", "props": {"quote": 0, "rely": {"formula": "", "fields": [], "type": "rely"}, "bizAlias": "", "label": "用车申请", "id": "FormRelateField_170GMGQ5VJMKG", "title": "", "fields": [{"componentName": "TextField", "props": {"placeholder": "其他车辆号牌，需手动输入", "label": "车辆号牌（其他）", "id": "TextField-JZDV6Z11", "required": true}}, {"componentName": "DDPhotoField", "props": {"watermark": true, "label": "出车里程表照片", "id": "DDPhotoField-IH4UNLKN", "required": true}}, {"componentName": "DDPhotoField", "props": {"watermark": true, "label": "到达或返车里程表照片", "id": "DDPhotoField-JO7YXNEU", "required": true}}, {"componentName": "CalculateField", "props": {"payEnable": false, "notUpper": "1", "formula": [{"id": "NumberField-JO7YXNET"}, "-", {"id": "NumberField-JO7YXNES"}], "placeholder": "自动计算数值", "label": "用车里程", "id": "CalculateField-JO7YXNER", "required": false}}], "dataSource": {"type": "form", "params": {"filter": "", "filters": [{"valueType": "fixed", "filterType": "EQ", "value": "审批通过", "fieldId": "proc_out_result"}]}, "target": {"appUuid": "", "formCode": "PROC-798D74F2-**************-DC238F2F57FF"}}, "required": true, "multi": 0}}, {"componentName": "NumberField", "props": {"unit": "公里", "payEnable": false, "bizAlias": "", "label": "请输入用车里程", "placeholder": "请输入数字", "id": "NumberField_L8FIU9RL5Q0W", "required": true, "ratio": 50}}], "componentName": "TableField", "props": {"tableViewMode": "table", "statField": [{"payEnable": false, "id": "NumberField_L8FIU9RL5Q0W", "label": "请输入用车里程"}], "bizAlias": "", "label": "私车公用里程数累计已达5000公里的用车审批单明细", "id": "TableField_13GZ7TELP4OW0", "actionName": "添加用车审批单"}}, {"children": [{"componentName": "FormRelateField", "props": {"quote": 0, "rely": {"formula": "", "fields": [], "type": "rely"}, "bizAlias": "", "label": "报销及补贴", "id": "FormRelateField_DOY31URXM3UO", "title": "", "fields": [{"componentName": "DDSelectField", "props": {"options": [{"value": "用车里程及附带费用报销补贴", "key": "option_1BJ80PW5WQGW0"}, {"value": "停车月租费补贴", "key": "option_21D7Q25L3BB4"}, {"value": "汽车保养费补贴", "key": "option_BKZFZUKJXQ00"}, {"value": "商业第三者责任保险费补贴", "key": "option_X9AFQDCGDSW0"}], "bizAlias": "", "label": "用车报销补贴类别", "placeholder": "请选择", "id": "DDSelectField_1QNJ57B2F8MPS", "required": true, "ratio": 50, "behaviorLinkage": [{"value": "option_BKZFZUKJXQ00", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_13GZ7TELP4OW0"}, {"behavior": "NORMAL", "fieldId": "MoneyField_1A0809XWS7XC0"}]}, {"value": "option_21D7Q25L3BB4", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_1E242SN33SAO0"}, {"behavior": "NORMAL", "fieldId": "MoneyField_1A0809XWS7XC0"}]}, {"value": "option_X9AFQDCGDSW0", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_NFK260J2CGG"}, {"behavior": "NORMAL", "fieldId": "MoneyField_1A0809XWS7XC0"}]}]}}, {"componentName": "MoneyField", "props": {"notUpper": "0", "bizAlias": "", "label": "报销补贴金额（元）", "placeholder": "请输入金额", "id": "MoneyField_1A0809XWS7XC0", "required": true}}, {"componentName": "CalculateField", "props": {"holidayOptions": [], "payEnable": true, "formula": [{"id": "MoneyField_1HOADFKIONUV4"}, "+", {"id": "MoneyField_1ZRFSP01U40ZK"}, "+", {"id": "MoneyField_1A0809XWS7XC0"}, "+", {"id": "MoneyField_1DWTQVO2C41S0"}, "-", {"id": "MoneyField_RJPOOC8E1LC0"}, "+", {"id": "MoneyField_1UXAARCJHAYO0"}], "bizAlias": "payCalculateField", "id": "MoneyField-JQVR5TPP", "placeholder": "自动计算总金额并关联支付，请勿填写", "label": "报销补贴总金额（元）", "required": false}}], "dataSource": {"type": "form", "params": {"filter": "", "filters": [{"valueType": "fixed", "filterType": "EQ", "value": "审批通过", "fieldId": "proc_out_result"}]}, "target": {"appUuid": "", "formCode": "PROC-1F14A304-A1E2-48FB-A57C-C717636AB54F"}}, "required": true, "multi": 0}}], "componentName": "TableField", "props": {"tableViewMode": "table", "bizAlias": "", "label": "停车月租补贴累计领取已达12次的审批单明细", "id": "TableField_NFK260J2CGG", "actionName": "添加用车审批单"}}, {"children": [{"componentName": "MoneyField", "props": {"payEnable": false, "notUpper": "0", "bizAlias": "", "label": "报销金额（元）", "placeholder": "请输入金额", "id": "MoneyField_1DWTQVO2C41S0", "required": true}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "采购", "key": "option_0"}, {"value": "文书和用印", "key": "option_1"}, {"extension": {"image": ""}, "value": "设备报修", "key": "option_EIDR64LH70O0"}], "bizAlias": "", "label": "报销的依据审批单", "placeholder": "请选择", "id": "DDSelectField_JCHZ1ASUQVK0", "required": true, "spread": false, "ratio": 50, "behaviorLinkage": [{"value": "option_0", "targets": [{"behavior": "NORMAL", "fieldId": "FormRelateField_KGG4ZDFPVYO0"}]}, {"value": "option_1", "targets": [{"behavior": "NORMAL", "fieldId": "FormRelateField_W00K8SKS5HC0"}]}, {"value": "option_EIDR64LH70O0", "targets": [{"behavior": "NORMAL", "fieldId": "FormRelateField_22YWUKH2AFSW0"}]}]}}, {"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "", "label": "采购", "id": "FormRelateField_KGG4ZDFPVYO0", "title": "", "fields": [{"componentName": "DDSelectField", "props": {"options": [{"value": "使用非奖励经费", "key": "option_0"}, {"value": "仅使用奖励经费", "key": "option_1"}], "bizAlias": "", "label": "是否使用非奖励经费", "placeholder": "请选择", "id": "DDSelectField_2099OMQS82ZK0", "required": true, "spread": false, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "DepartmentField", "props": {"multiple": true, "bizAlias": "", "placeholder": "请选择", "label": "使用部门", "id": "DepartmentField-JZWBD0FS", "required": true}}, {"children": [{"componentName": "TextField", "props": {"placeholder": "请输入", "label": "名称", "id": "TextField-IH4T9JQC", "componentName": "TextField", "required": true}}, {"componentName": "MoneyField", "props": {"placeholder": "请输入金额", "label": "单价（元）", "id": "MoneyField-JZWAQB5R", "required": true}}, {"componentName": "NumberField", "props": {"placeholder": "请输入", "label": "数量", "id": "NumberField-IH4T9JQF", "required": true}}, {"componentName": "CalculateField", "props": {"holidayOptions": [], "payEnable": false, "formula": [{"id": "MoneyField-JZWAQB5R"}, "*", {"id": "NumberField-IH4T9JQF"}], "bizAlias": "payCalculateField", "id": "NumberField-IH4T9JQG", "placeholder": "请输入", "label": "总价", "required": true}}, {"componentName": "TextNote", "props": {"notPrint": "1", "id": "TextNote-IH4T9JQL", "content": "如需采购多种产品，请点击“增加明细”"}}], "componentName": "TableField", "props": {"statField": [{"upper": true, "id": "NumberField-IH4T9JQG", "label": "总价"}], "label": "采购明细", "id": "TableField-IH4T9JQB", "actionName": "增加明细"}}, {"componentName": "ExternalContactField", "props": {"bizAlias": "", "label": "供应商联系方式", "placeholder": "请选择", "id": "ExternalContactField_A1BWCPFFNG80", "required": true}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-71B17B92-9E25-4795-8D78-DC923391FD22", "appType": 0}}, "required": true, "multi": 0}}, {"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "", "label": "文书和用印", "id": "FormRelateField_W00K8SKS5HC0", "title": "", "fields": [{"componentName": "MoneyField", "props": {"placeholder": "请输入金额", "label": "采购金额（元）", "id": "MoneyField-K7LMC3LK", "required": true}}, {"componentName": "ExternalContactField", "props": {"bizAlias": "", "label": "供应商/客户联系方式", "placeholder": "请选择", "id": "ExternalContactField_DQ8R91J6IW00", "required": true}}, {"componentName": "DDAttachment", "props": {"eSign": false, "appId": "3179", "notPrint": "0", "label": "请全文上传需要审批的文件", "id": "DDAttachment-JN91QXS6", "required": true, "needPreSign": false}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-689BB3DA-16C3-4480-897A-60575517AF6C", "appType": 0}}, "required": true, "multi": 0}}, {"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "", "label": "设备报修", "id": "FormRelateField_22YWUKH2AFSW0", "title": "", "fields": [{"children": [{"componentName": "TextField", "props": {"placeholder": "请输入", "label": "维修内容", "id": "TextField-JTTH6CN5", "componentName": "TextField", "required": true}}, {"componentName": "MoneyField", "props": {"bizAlias": "", "placeholder": "请输入金额", "label": "维修费用（元）", "id": "MoneyField-JTTH6CN3", "required": true}}, {"componentName": "TextField", "props": {"bizAlias": "", "label": "备注", "placeholder": "请输入", "id": "TextField_1RUI0IJHYVSW0", "required": false, "ratio": 50}}], "componentName": "TableField", "props": {"tableViewMode": "table", "statField": [{"upper": true, "id": "MoneyField-JTTH6CN3", "label": "维修费用（元）"}], "label": "维修明细", "id": "TableField-JTTH3VO2", "actionName": "增加明细"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-5FYJ2Z2W-3Q7382AI0J8NRBIY7TJN3-ZYFJ3KSJ-E2", "appType": 0}}, "required": true, "multi": 0}}], "componentName": "TableField", "props": {"tableViewMode": "table", "statField": [{"payEnable": false, "upper": true, "id": "MoneyField_1DWTQVO2C41S0", "label": "报销金额（元）"}], "bizAlias": "", "label": "报销明细", "id": "TableField_7HQJAT8LO840", "actionName": "添加审批单"}}, {"componentName": "DDSelectField", "props": {"options": [{"value": "是", "key": "option_0"}, {"value": "否", "key": "option_1"}], "bizAlias": "", "label": "是否借支备用金", "placeholder": "请选择", "id": "DDSelectField_HN6SY13GGEO0", "required": false, "spread": false, "ratio": 50, "behaviorLinkage": [{"value": "option_0", "targets": [{"behavior": "NORMAL", "fieldId": "TableField_92YLLM375DC0"}]}]}}, {"children": [{"componentName": "MoneyField", "props": {"payEnable": false, "notUpper": "0", "bizAlias": "", "label": "已借支金额（元）", "placeholder": "请输入金额", "id": "MoneyField_RJPOOC8E1LC0", "required": true}}, {"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "", "label": "备用金借用", "id": "FormRelateField_QEKALODXNOG0", "title": "", "fields": [{"componentName": "DDSelectField", "props": {"options": [{"value": "备用金", "key": "option_0"}, {"value": "资产处置项目", "key": "option_1"}, {"value": "其他", "key": "option_2"}], "bizAlias": "", "placeholder": "请选择", "label": "事由", "id": "DDSelectField-K2D3TWGR", "required": true, "behaviorLinkage": [{"value": "option_2", "targets": [{"behavior": "NORMAL", "fieldId": "DDPhotoField_PYZ8V8XG3E2O"}]}, {"value": "option_0", "targets": [{"behavior": "NORMAL", "fieldId": "RelateField_1PB8GTPQF20W0"}]}]}}, {"children": [{"componentName": "TextField", "props": {"placeholder": "请输入", "label": "用途", "id": "TextField-K2E2C1KG", "componentName": "TextField", "required": true}}, {"componentName": "MoneyField", "props": {"payEnable": true, "notUpper": "0", "bizAlias": "", "placeholder": "请输入金额", "label": "金额（元）", "id": "MoneyField-K2E2C1KF", "required": true}}, {"componentName": "DDDateRangeField", "props": {"unit": "天", "format": "yyyy-MM-dd", "placeholder": "请选择", "label": ["使用日期", "归还日期"], "id": "DDDateRangeField-IH36UD72", "componentName": "DDDateRangeField", "pushToCalendar": "0", "required": true, "push": {"attendanceRule": 1}}}], "componentName": "TableField", "props": {"statField": [{"upper": true, "id": "MoneyField-K2E2C1KF", "label": "金额（元）"}], "label": "款项明细", "id": "TableField-K2E2BSUT", "actionName": "增加明细"}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-FFC99B3D-9A41-4926-9056-5239A6B248EF", "appType": 0}}, "required": true, "multi": 0}}], "componentName": "TableField", "props": {"tableViewMode": "table", "statField": [{"payEnable": false, "upper": true, "id": "MoneyField_RJPOOC8E1LC0", "label": "已借支金额（元）"}], "bizAlias": "", "label": "备用金借支明细", "id": "TableField_92YLLM375DC0", "actionName": "添加备用金借支审批单"}}, {"componentName": "FormRelateField", "props": {"quote": 1, "bizAlias": "", "label": "物品领用", "id": "FormRelateField_11CZ74EVDHZ40", "title": "", "fields": [{"componentName": "DDSelectField", "props": {"options": [{"value": "办公设备", "key": "option_0"}, {"value": "办公用品", "key": "option_1"}, {"value": "工作服", "key": "option_KE85IO2C"}, {"value": "其他", "key": "option_KE85JGO2"}], "placeholder": "请选择", "label": "物品类别", "id": "DDSelectField-K93M1M00", "required": true, "behaviorLinkage": [{"value": "option_KE85IO2C", "targets": [{"behavior": "NORMAL", "fieldId": "SignatureField_20YH4M3LHKYDC"}]}]}}, {"children": [{"componentName": "TextField", "props": {"placeholder": "请输入物品名称", "label": "物品名称", "id": "物品名称", "componentName": "TextField", "required": true}}, {"componentName": "NumberField", "props": {"placeholder": "请输入数量", "label": "数量", "id": "数量"}}], "componentName": "TableField", "props": {"statField": [], "label": "物品明细", "id": "TableField-MINGXI", "actionName": "增加物品明细"}}, {"componentName": "SignatureField", "props": {"readFromLast": false, "label": "请签名确认已领取", "id": "SignatureField_20YH4M3LHKYDC", "required": true}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-C6A0FB15-90EF-42E9-A9E1-3E49DB9C94D7", "appType": 0}}, "required": true, "multi": 0}}, {"componentName": "CalculateField", "props": {"holidayOptions": [], "payEnable": true, "formula": [{"id": "MoneyField_1HOADFKIONUV4"}, "+", {"id": "MoneyField_1ZRFSP01U40ZK"}, "+", {"id": "MoneyField_1A0809XWS7XC0"}, "+", {"id": "MoneyField_1DWTQVO2C41S0"}, "-", {"id": "MoneyField_RJPOOC8E1LC0"}, "+", {"id": "MoneyField_1UXAARCJHAYO0"}], "bizAlias": "payCalculateField", "id": "MoneyField-JQVR5TPP", "placeholder": "自动计算总金额并关联支付，请勿填写", "label": "报销补贴总金额（元）", "required": false}}, {"componentName": "RecipientAccountField", "props": {"holidayOptions": [], "bizAlias": "collectionAccount", "id": "CollectionAccount-X7SMT6SL", "placeholder": "请选择钉钉、支付宝或银行账户", "label": "收款账户", "required": true}}, {"componentName": "TextareaField", "props": {"bizAlias": "", "label": "备注", "placeholder": "请输入", "id": "TextareaField_Z73FO6QMCLC0", "required": false}}, {"componentName": "DDAttachment", "props": {"label": "附件", "id": "DDAttachment_2DHIAN7U1TNO", "required": false}}, {"componentName": "InvoiceField", "props": {"label": "电子发票", "id": "InvoiceField-JQVQV04H"}}, {"componentName": "TextareaField", "props": {"placeholder": "若提交发票为电子发票，需填写发票右上角第二行发票号码", "label": "电子发票号码", "id": "TextareaField-JZWEXZEY"}}]