{"code": 200, "body": {"result": [{"leaveUnit": "halfDay", "naturalDayLeave": false, "perHoursInDay": 0, "paidLeave": false, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "5b4c52a0-ba17-4a79-bfb2-95117d5eec3f", "leaveName": "不限制余额啊1111f11", "leaveHourCeil": "", "modifyTime": 1724378501000, "operatorId": "1662260366696151", "leaveStatisticType": "freedom", "status": true}, {"operatorId": "1662260366696151", "status": true, "leaveUnit": "day", "naturalDayLeave": false, "annualLeaveRule": {"leaveRuleType": "<PERSON><PERSON><PERSON>", "whenToRelease": "new_year_day", "expireDay": 0, "expireDateType": "one_year", "expireTimeUnit": "day", "balanceRule": [1], "howToRelease": "manual", "lessThanOneYearRound": false, "leaveCode": "cc754b3c-f75e-4bf9-b47f-5e0ca88fc843", "bizType": "general_leave", "text": "手动发放", "workBalanceRule": []}, "paidLeave": false, "perHoursInDay": 0, "remindRule": {"remindDateNum": 1, "remindDateUnit": "month", "remindSwitch": false, "remindRange": ["manager", "employee"], "type": "REMIND_DEFAULT"}, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "cc754b3c-f75e-4bf9-b47f-5e0ca88fc843", "bizType": "general_leave", "leaveHourCeil": "", "leaveName": "单位测试1", "modifyTime": 1724378501000, "leaveBalanceQuotaVo": {"leaveCode": "cc754b3c-f75e-4bf9-b47f-5e0ca88fc843", "quotaNumPerHour": 0, "visible": true, "userId": "1662260366696151", "quotaNumPerDay": 0, "corpId": "ding523a0806f689b0aaf2c783f7214b6d69"}, "leaveStatisticType": "balance_cycle"}, {"operatorId": "013041240324246307", "status": true, "leaveUnit": "day", "naturalDayLeave": false, "annualLeaveRule": {"leaveRuleType": "<PERSON><PERSON><PERSON>", "whenToRelease": "new_year_day", "expireDay": 0, "expireDateType": "one_year", "expireTimeUnit": "day", "balanceRule": [100], "howToRelease": "year", "lessThanOneYearRound": false, "leaveCode": "c170818c-aacc-4ff5-bf5a-c85a7dcd2984", "bizType": "general_leave", "text": "每年1月1日自动发放，100天", "workBalanceRule": []}, "paidLeave": false, "perHoursInDay": 0, "remindRule": {"remindDateNum": 1, "remindDateUnit": "month", "remindSwitch": false, "remindRange": ["manager", "employee"], "type": "REMIND_DEFAULT"}, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "c170818c-aacc-4ff5-bf5a-c85a7dcd2984", "bizType": "general_leave", "leaveHourCeil": "", "leaveName": "事假（按天）", "modifyTime": 1724378501000, "leaveBalanceQuotaVo": {"leaveCode": "c170818c-aacc-4ff5-bf5a-c85a7dcd2984", "quotaNumPerHour": 0, "visible": true, "userId": "1662260366696151", "quotaNumPerDay": 100, "corpId": "ding523a0806f689b0aaf2c783f7214b6d69"}, "leaveStatisticType": "balance_cycle"}, {"operatorId": "013041240324246307", "status": true, "leaveUnit": "hour", "naturalDayLeave": false, "annualLeaveRule": {"expireTimeUnit": "day", "leaveRuleType": "WorkOverTime", "expireDate": "12-31", "expireDateType": "absolute_time", "bizType": "lieu_leave", "expireDay": 0, "leaveCode": "5ca23a18-e931-4d13-9369-4cff5dbb17e7", "text": "加班时长自动计入调休余额", "whenToRelease": "new_year_day", "howToRelease": "work_time"}, "paidLeave": false, "perHoursInDay": 0, "remindRule": {"remindDateNum": 1, "remindDateUnit": "month", "remindSwitch": false, "remindRange": ["manager", "employee"], "type": "REMIND_DEFAULT"}, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "5ca23a18-e931-4d13-9369-4cff5dbb17e7", "bizType": "lieu_leave", "leaveHourCeil": "", "leaveName": "调休@", "modifyTime": 1724378501000, "leaveBalanceQuotaVo": {"leaveCode": "5ca23a18-e931-4d13-9369-4cff5dbb17e7", "quotaNumPerHour": 0, "visible": true, "userId": "1662260366696151", "quotaNumPerDay": 0, "corpId": "ding523a0806f689b0aaf2c783f7214b6d69"}, "leaveStatisticType": "balance_cycle_freedom"}, {"leaveUnit": "day", "naturalDayLeave": false, "perHoursInDay": 0, "paidLeave": false, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "69ff9366-70e2-4702-8fed-43e83ad2e45b", "leaveName": "不限制余额", "leaveHourCeil": "", "modifyTime": 1724378501000, "operatorId": "013041240324246307", "leaveStatisticType": "freedom", "status": true}, {"operatorId": "013041240324246307", "status": true, "leaveUnit": "day", "naturalDayLeave": false, "annualLeaveRule": {"leaveRuleType": "<PERSON><PERSON><PERSON>", "whenToRelease": "new_year_day", "expireDay": 0, "expireDateType": "one_year", "expireTimeUnit": "day", "balanceRule": [1], "howToRelease": "manual", "lessThanOneYearRound": false, "leaveCode": "f95ae3c7-d3bc-4b00-b4f2-f35bde8dbb71", "bizType": "general_leave", "text": "手动发放", "workBalanceRule": []}, "paidLeave": false, "perHoursInDay": 0, "remindRule": {"remindDateNum": 1, "remindDateUnit": "month", "remindSwitch": false, "remindRange": ["manager", "employee"], "type": "REMIND_DEFAULT"}, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "f95ae3c7-d3bc-4b00-b4f2-f35bde8dbb71", "bizType": "general_leave", "leaveHourCeil": "", "leaveName": "手动发放", "modifyTime": 1724378501000, "leaveBalanceQuotaVo": {"leaveCode": "f95ae3c7-d3bc-4b00-b4f2-f35bde8dbb71", "quotaNumPerHour": 0, "visible": true, "userId": "1662260366696151", "quotaNumPerDay": 0, "corpId": "ding523a0806f689b0aaf2c783f7214b6d69"}, "leaveStatisticType": "balance_cycle"}, {"naturalDayLeave": false, "modifyTime": 1724378501000, "paidLeave": false, "source": "inner", "status": true, "visibilityRules": [{"type": "label", "visible": ["1588073953"]}], "perHoursInDay": 0, "leaveCode": "617dbe25-37ac-4fb8-9af3-55c3300b3249", "leaveUnit": "hour", "whenCanLeave": "formal", "leaveName": "月假", "canDelete": true, "remindRule": {"remindDateNum": 1, "remindDateUnit": "month", "remindSwitch": false, "remindRange": ["manager", "employee"], "type": "REMIND_DEFAULT"}, "operatorId": "021617506121773936", "leaveHourCeil": "", "bizType": "general_leave", "leaveBalanceQuotaVo": {"leaveCode": "617dbe25-37ac-4fb8-9af3-55c3300b3249", "quotaNumPerHour": 0, "visible": true, "userId": "1662260366696151", "quotaNumPerDay": 0, "corpId": "ding523a0806f689b0aaf2c783f7214b6d69"}, "leaveStatisticType": "balance_cycle", "leaveTimeCeilMinUnit": "hour", "annualLeaveRule": {"leaveRuleType": "<PERSON><PERSON><PERSON>", "whenToRelease": "new_year_day", "expireDay": 12, "expireDateType": "one_year", "expireTimeUnit": "month", "balanceRule": [1], "howToRelease": "manual", "lessThanOneYearRound": false, "leaveCode": "617dbe25-37ac-4fb8-9af3-55c3300b3249", "bizType": "general_leave", "text": "手动发放", "workBalanceRule": []}}, {"leaveUnit": "day", "naturalDayLeave": false, "perHoursInDay": 0, "paidLeave": false, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "35d8e007-c97c-4662-93f6-0830d075d602", "leaveName": "啊啊啊", "leaveHourCeil": "", "modifyTime": 1724378501000, "operatorId": "653436570024052244", "leaveStatisticType": "freedom", "status": true}, {"operatorId": "013041240324246307", "status": true, "leaveUnit": "day", "naturalDayLeave": false, "annualLeaveRule": {"leaveRuleType": "<PERSON><PERSON><PERSON>", "whenToRelease": "new_year_day", "expireDay": 0, "expireDateType": "one_year", "expireTimeUnit": "day", "balanceRule": [5], "howToRelease": "year", "lessThanOneYearRound": false, "leaveCode": "0d1d15a7-271a-4fee-abfd-8f7f3ba856d5", "bizType": "general_leave", "text": "每年1月1日自动发放，5天", "workBalanceRule": []}, "paidLeave": false, "perHoursInDay": 0, "remindRule": {"remindDateNum": 1, "remindDateUnit": "month", "remindSwitch": false, "remindRange": ["manager", "employee"], "type": "REMIND_DEFAULT"}, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "0d1d15a7-271a-4fee-abfd-8f7f3ba856d5", "bizType": "general_leave", "leaveHourCeil": "", "leaveName": "测试", "modifyTime": 1724378501000, "leaveBalanceQuotaVo": {"leaveCode": "0d1d15a7-271a-4fee-abfd-8f7f3ba856d5", "quotaNumPerHour": 0, "visible": true, "userId": "1662260366696151", "quotaNumPerDay": 5, "corpId": "ding523a0806f689b0aaf2c783f7214b6d69"}, "leaveStatisticType": "balance_cycle"}, {"leaveUnit": "halfDay", "naturalDayLeave": false, "perHoursInDay": 0, "paidLeave": false, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "c5a192ab-0e9c-4014-b696-291e7cb77584", "leaveName": "半天假", "leaveHourCeil": "", "modifyTime": 1724378501000, "operatorId": "013041240324246307", "leaveStatisticType": "freedom", "status": true}, {"leaveUnit": "day", "naturalDayLeave": false, "perHoursInDay": 0, "paidLeave": false, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "36980a89-1155-44d0-8a52-776de487be9b", "leaveName": "全天假", "leaveHourCeil": "", "modifyTime": 1724378501000, "operatorId": "133365575623316778", "leaveStatisticType": "freedom", "status": true}, {"operatorId": "013041240324246307", "status": true, "leaveUnit": "day", "naturalDayLeave": true, "annualLeaveRule": {"leaveRuleType": "<PERSON><PERSON><PERSON>", "whenToRelease": "new_year_day", "expireDay": 0, "expireDateType": "one_year", "expireTimeUnit": "day", "balanceRule": [5], "howToRelease": "year", "lessThanOneYearRound": false, "leaveCode": "fc8d2e7c-39ee-4110-b18f-b8ca6305ba94", "bizType": "general_leave", "text": "每年1月1日自动发放，5天", "workBalanceRule": []}, "paidLeave": false, "perHoursInDay": 0, "remindRule": {"remindDateNum": 1, "remindDateUnit": "month", "remindSwitch": false, "remindRange": ["manager", "employee"], "type": "REMIND_DEFAULT"}, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "fc8d2e7c-39ee-4110-b18f-b8ca6305ba94", "bizType": "general_leave", "leaveHourCeil": "", "leaveName": "自然日", "modifyTime": 1724378501000, "leaveBalanceQuotaVo": {"leaveCode": "fc8d2e7c-39ee-4110-b18f-b8ca6305ba94", "quotaNumPerHour": 0, "visible": true, "userId": "1662260366696151", "quotaNumPerDay": 5, "corpId": "ding523a0806f689b0aaf2c783f7214b6d69"}, "leaveStatisticType": "balance_cycle"}, {"operatorId": "013041240324246307", "status": true, "leaveUnit": "halfDay", "naturalDayLeave": true, "annualLeaveRule": {"leaveRuleType": "<PERSON><PERSON><PERSON>", "whenToRelease": "new_year_day", "expireDay": 0, "expireDateType": "one_year", "expireTimeUnit": "day", "balanceRule": [50], "howToRelease": "year", "lessThanOneYearRound": false, "leaveCode": "0e441e43-676f-4864-aa00-edbe1fdf182f", "bizType": "general_leave", "text": "每年1月1日自动发放，50天", "workBalanceRule": []}, "paidLeave": false, "perHoursInDay": 0, "remindRule": {"remindDateNum": 1, "remindDateUnit": "month", "remindSwitch": false, "remindRange": ["manager", "employee"], "type": "REMIND_DEFAULT"}, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "0e441e43-676f-4864-aa00-edbe1fdf182f", "bizType": "general_leave", "leaveHourCeil": "", "leaveName": "自然日（半天）", "modifyTime": 1724378501000, "leaveBalanceQuotaVo": {"leaveCode": "0e441e43-676f-4864-aa00-edbe1fdf182f", "quotaNumPerHour": 0, "visible": true, "userId": "1662260366696151", "quotaNumPerDay": 50, "corpId": "ding523a0806f689b0aaf2c783f7214b6d69"}, "leaveStatisticType": "balance_cycle"}, {"leaveUnit": "halfDay", "naturalDayLeave": true, "perHoursInDay": 0, "paidLeave": false, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "98c100b9-6885-4068-9a50-f356f782a0bf", "leaveName": "自然日半天", "leaveHourCeil": "", "modifyTime": 1724378501000, "operatorId": "013041240324246307", "leaveStatisticType": "freedom", "status": true}, {"operatorId": "1662260366696151", "status": true, "leaveUnit": "day", "naturalDayLeave": false, "annualLeaveRule": {"leaveRuleType": "<PERSON><PERSON><PERSON>", "whenToRelease": "new_year_day", "expireDay": 0, "expireDateType": "one_year", "expireTimeUnit": "day", "balanceRule": [5], "howToRelease": "year", "lessThanOneYearRound": false, "leaveCode": "bc36dbbe-db52-47ba-8475-119f039f3811", "bizType": "general_leave", "text": "每年1月1日自动发放，5天", "workBalanceRule": []}, "paidLeave": false, "perHoursInDay": 0, "remindRule": {"remindDateNum": 1, "remindDateUnit": "month", "remindSwitch": false, "remindRange": ["manager", "employee"], "type": "REMIND_DEFAULT"}, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "bc36dbbe-db52-47ba-8475-119f039f3811", "bizType": "general_leave", "leaveHourCeil": "", "leaveName": "测试111", "modifyTime": 1724378501000, "leaveBalanceQuotaVo": {"leaveCode": "bc36dbbe-db52-47ba-8475-119f039f3811", "quotaNumPerHour": 0, "visible": true, "userId": "1662260366696151", "quotaNumPerDay": 5, "corpId": "ding523a0806f689b0aaf2c783f7214b6d69"}, "leaveStatisticType": "balance_cycle"}, {"status": true, "leaveUnit": "day", "naturalDayLeave": false, "perHoursInDay": 0, "paidLeave": true, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "30c14037-0416-4de3-90e1-a0dbd5fda053", "bizType": "breastfeeding_leave_new", "leaveHourCeil": "", "leaveName": "哺乳假测试", "modifyTime": 1724378501000, "leaveStatisticType": "freedom", "operatorId": "013041240324246307"}, {"operatorId": "013041240324246307", "status": true, "leaveUnit": "day", "naturalDayLeave": false, "annualLeaveRule": {"leaveRuleType": "<PERSON><PERSON><PERSON>", "whenToRelease": "new_year_day", "expireDay": 0, "expireDateType": "one_year", "expireTimeUnit": "day", "balanceRule": [5], "howToRelease": "year", "lessThanOneYearRound": false, "leaveCode": "b9f278a5-08ca-451d-8953-45ee6a68366d", "bizType": "general_leave", "text": "每年1月1日自动发放，5天", "workBalanceRule": []}, "paidLeave": false, "perHoursInDay": 0, "remindRule": {"remindDateNum": 1, "remindDateUnit": "month", "remindSwitch": false, "remindRange": ["manager", "employee"], "type": "REMIND_DEFAULT"}, "source": "inner", "whenCanLeave": "entry", "leaveTimeCeilMinUnit": "hour", "canDelete": true, "leaveCode": "b9f278a5-08ca-451d-8953-45ee6a68366d", "bizType": "general_leave", "leaveHourCeil": "", "leaveName": "年假", "modifyTime": 1724378501000, "leaveBalanceQuotaVo": {"leaveCode": "b9f278a5-08ca-451d-8953-45ee6a68366d", "quotaNumPerHour": 0, "visible": true, "userId": "1662260366696151", "quotaNumPerDay": 5, "corpId": "ding523a0806f689b0aaf2c783f7214b6d69"}, "leaveStatisticType": "balance_cycle"}], "success": true}}