{"code": 200, "body": {"result": [{"uid": 6833116, "receiptorName": "千下", "orgId": *********, "extension": {"avatarMediaId": "@lALPDetfZrQC8cTNA-jNA-g", "isMySelf": "true", "mobile": "153*******1"}, "receiptorType": "DING_USER", "receiptorId": "6833116", "receiptorRemark": "自己", "isSelf": true, "id": "1"}, {"uid": 6833116, "id": "202412241173389137", "receiptorName": "哈哈哈哈", "orgId": *********, "extension": {"BANK_EXT_INST_PROVINCE": "福建省", "BANK_EXT_INST_BRANCH_NAME": "中国民生银行股份有限公司宁德分行", "BANK_EXT_INST_BRANCH_CODE": "123456", "BANK_EXT_INST_CITY": "宁德市", "BANK_EXT_INST_NAME": "中国民生银行", "BANK_EXT_INST_CODE": "CMBC", "BANK_EXT_ACCOUNT_TYPE": "TO_PUB", "BANK_EXT_CUSTOM_BRANCH_CODE": "123", "BANK_EXT_CUSTOM_BRANCH_NAME": "中国民生银行股份有限公司宁德分行111"}, "receiptorType": "BANKCARD", "receiptorId": "4488-7*8865"}, {"uid": 6833116, "id": "202412241173385149", "receiptorName": "159", "orgId": *********, "extension": {"BANK_EXT_INST_PROVINCE": "广东省", "BANK_EXT_INST_BRANCH_NAME": "兴业银行股份有限公司广州珠江支行", "BANK_EXT_INST_CITY": "广州市", "BANK_EXT_INST_NAME": "兴业银行", "BANK_EXT_INST_CODE": "CIB", "BANK_EXT_ACCOUNT_TYPE": "TO_PUB"}, "receiptorType": "BANKCARD", "receiptorId": "1597-44488"}, {"uid": 6833116, "id": "202412241072954001", "receiptorName": "Lllllll", "orgId": *********, "extension": {"BANK_EXT_INST_PROVINCE": "安徽省", "BANK_EXT_INST_BRANCH_NAME": "中国建设银行股份有限公司安庆中兴大道支行", "BANK_EXT_INST_CITY": "安庆市", "BANK_EXT_INST_NAME": "中国建设银行", "BANK_EXT_INST_CODE": "CCB", "BANK_EXT_ACCOUNT_TYPE": "TO_PRI"}, "receiptorType": "BANKCARD", "receiptorId": "********", "receiptorRemark": "Ffds"}, {"uid": 6833116, "id": "202412232073419370", "receiptorName": "阿斯蒂芬撒旦法", "orgId": *********, "extension": {"BANK_EXT_INST_PROVINCE": "河北省", "BANK_EXT_INST_BRANCH_NAME": "中国银行股份有限公司雄县支行", "BANK_EXT_INST_CITY": "保定市", "BANK_EXT_INST_NAME": "中国银行", "BANK_EXT_INST_CODE": "BOC", "BANK_EXT_ACCOUNT_TYPE": "TO_PUB"}, "receiptorType": "BANKCARD", "receiptorId": "空了；sas*'f萨芬", "receiptorRemark": ""}, {"uid": 6833116, "id": "202412101972996001", "receiptorName": "杭州普回贸易有限公司", "orgId": *********, "extension": {"BANK_EXT_INST_PROVINCE": "浙江省", "BANK_EXT_INST_BRANCH_NAME": "浙江网商银行股份有限公司", "BANK_EXT_INST_CITY": "杭州市", "BANK_EXT_INST_NAME": "网商银行", "BANK_EXT_INST_CODE": "ANTBANK", "BANK_EXT_ACCOUNT_TYPE": "TO_PUB"}, "receiptorType": "BANKCARD", "receiptorId": "888888******1744", "receiptorRemark": ""}, {"uid": 6833116, "id": "202412021172323623", "receiptorName": "杭州普回贸易有限公司", "orgId": *********, "receiptorType": "ALIPAY", "receiptorId": "769***@qq.com", "receiptorRemark": ""}, {"source": "supplier", "uid": 6833116, "id": "SUP_1023A742034B213374E4000Q", "receiptorName": "123", "orgId": *********, "extension": {"BANK_EXT_INST_BRANCH_NAME": "杭州银行股份有限公司营业部", "BANK_EXT_ACCOUNT_TYPE": "TO_PUB", "BANK_EXT_INST_PROVINCE": "浙江省", "BANK_EXT_INST_CITY": "杭州市", "BANK_EXT_BANK_CODE": "************", "BANK_EXT_INST_CODE": "杭州银行", "BANK_EXT_INST_NAME": "杭州银行"}, "receiptorType": "BANKCARD", "receiptorId": "123"}], "total": 15}}