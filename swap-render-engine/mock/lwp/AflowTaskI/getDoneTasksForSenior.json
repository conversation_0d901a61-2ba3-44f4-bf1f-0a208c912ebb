{"code": 200, "body": {"values": [{"originatorName": "玉岩", "processInstanceStatus": "COMPLETED", "origin": "<PERSON><PERSON><PERSON>", "businessId": "202009091041000112921", "originator": {"workNo": "0107200036940608", "workNoType": "STAFFID", "name": "玉岩", "isSysAdmin": false, "userId": "0107200036940608", "personalPhoto": "https://static.dingtalk.com/media/lADPDgQ9rEI8hKHNBgDNBgM_1539_1536.jpg"}, "title": "玉岩提交的布局测试1", "outResult": "agree", "attendanceType": 0, "processId": ***********, "formMessage": "单选框:选项3<br>单行输入框:2222<br>单行输入框:撒旦法士大夫", "originatorId": "0107200036940608", "bizStatusShow": "审批通过", "email": [], "attachedProcessInstances": [], "actionerName": [], "processInstanceId": "63588523-8b29-4dc6-b46e-70d72e619764", "finishTime": 1599619272000, "formValueList": [{"label": "单选框", "value": "选项3"}, {"label": "单行输入框", "value": "2222"}, {"label": "单行输入框", "value": "撒旦法士大夫"}], "boxsterUrl": "https://aflow.dingtalk.com/dingtalk/mobile/homepage.htm?corpid=ding4e03c0afa42b32c835c2f4657eb6378f&amp;dd_share=false&amp;showmenu=true&amp;dd_progress=false&amp;back=native&amp;procInstId=63588523-8b29-4dc6-b46e-70d72e619764&amp;swfrom=oa&amp;dinghash=approval#approval", "originatorPhoto": "https://static.dingtalk.com/media/lADPDgQ9rEI8hKHNBgDNBgM_1539_1536.jpg", "createTime": 1599619271000, "processCode": "PROC-EE1B13C3-0000-475B-8EAE-2719BE4B64D6", "actionerId": [], "disableRevert": false, "attributes": {"attendanceType": "NONE", "appendEnable": "n", "processName": "布局测试1", "bizCategoryId": "", "forecast": "{\"forecastSuccess\":true,\"processCode\":\"PROC-EE1B13C3-0000-475B-8EAE-2719BE4B64D6\",\"processId\":***********,\"staticWorkflow\":true,\"userId\":\"0107200036940608\",\"workflowActionerRules\":[],\"workflowForecastNodes\":[{\"activityId\":\"sid-startevent\",\"outId\":\"line-random-sid-startevent-2a13_91de\"},{\"activityId\":\"2a13_91de\",\"outId\":\"635b_c513\"},{\"activityId\":\"endId\"}]}", "workflowVersion": "2", "originatorUid": "26509036", "bizSceneCategory": "unclassified", "processVersion": "15", "handSignConf": "{\"handSignEnable\":false,\"resignEnable\":false}", "scene": "", "procType": "inner"}, "sourceName": "审批", "workflowVersion": "2", "taskId": [], "procType": "inner"}], "hasMore": false, "totalCount": 0}}