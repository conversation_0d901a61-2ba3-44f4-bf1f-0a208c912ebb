{"code": 200, "statusCode": 200, "body": {"data": [{"id": "202008171909362470", "orgId": ********, "receiptorId": "***********", "receiptorName": "许凌志", "receiptorType": "ALIPAY", "uid": ********}, {"id": "202008181509543003", "orgId": ********, "receiptorId": "*********", "receiptorName": "chen<PERSON><PERSON>", "receiptorType": "DING_USER", "extension": {"jobNumber": ""}}, {"extension": {"BANK_EXT_ACCOUNT_TYPE": "TO_PUB", "BANK_EXT_INST_BRANCH_NAME": "中国建设银行安庆分行会计结算部", "BANK_EXT_INST_CITY": "安庆市", "BANK_EXT_INST_CODE": "CCB", "BANK_EXT_INST_NAME": "中国建设银行", "BANK_EXT_INST_PROVINCE": "安徽省"}, "id": "202008171909909399", "orgId": ********, "receiptorId": "110001**8272", "receiptorName": "幸福预报", "receiptorType": "BANKCARD", "uid": ********}, {"extension": {"BANK_EXT_ACCOUNT_TYPE": "TO_PRI", "BANK_EXT_INST_CODE": "CMB", "BANK_EXT_INST_NAME": "招商银行"}, "id": "202008171909901447", "orgId": ********, "receiptorId": "621485******2582", "receiptorName": "许凌志", "receiptorType": "BANKCARD", "uid": ********}, {"id": "202008171909346500", "orgId": ********, "receiptorId": "********", "receiptorName": "空明", "receiptorType": "DING_USER", "uid": ********, "extension": {"avatarMediaId": "@lADPDgQ9q0Os223NBNrNBNo", "jobNumber": "manager5022"}}, {"id": "1", "isSelf": true, "orgId": ********, "receiptorId": "********", "receiptorName": "玉岩", "receiptorType": "DING_USER", "uid": ********, "extension": {"avatarMediaId": "@lADPDgQ9rEI8hKHNBgDNBgM", "isMySelf": "true", "jobNumber": ""}}], "success": true}, "responseText": {"data": [{"id": "202008171909362470", "orgId": ********, "receiptorId": "***********", "receiptorName": "许凌志", "receiptorType": "ALIPAY", "uid": ********}, {"id": "202008181509543003", "orgId": ********, "receiptorId": "*********", "receiptorName": "chen<PERSON><PERSON>", "receiptorType": "DING_USER", "extension": {"jobNumber": ""}}, {"extension": {"BANK_EXT_ACCOUNT_TYPE": "TO_PUB", "BANK_EXT_INST_BRANCH_NAME": "中国建设银行安庆分行会计结算部", "BANK_EXT_INST_CITY": "安庆市", "BANK_EXT_INST_CODE": "CCB", "BANK_EXT_INST_NAME": "中国建设银行", "BANK_EXT_INST_PROVINCE": "安徽省"}, "id": "202008171909909399", "orgId": ********, "receiptorId": "110001**8272", "receiptorName": "幸福预报", "receiptorType": "BANKCARD", "uid": ********}, {"extension": {"BANK_EXT_ACCOUNT_TYPE": "TO_PRI", "BANK_EXT_INST_CODE": "CMB", "BANK_EXT_INST_NAME": "招商银行"}, "id": "202008171909901447", "orgId": ********, "receiptorId": "621485******2582", "receiptorName": "许凌志", "receiptorType": "BANKCARD", "uid": ********}, {"id": "202008171909346500", "orgId": ********, "receiptorId": "********", "receiptorName": "空明", "receiptorType": "DING_USER", "uid": ********, "extension": {"avatarMediaId": "@lADPDgQ9q0Os223NBNrNBNo", "jobNumber": "manager5022"}}, {"id": "1", "isSelf": true, "orgId": ********, "receiptorId": "********", "receiptorName": "玉岩", "receiptorType": "DING_USER", "uid": ********, "extension": {"avatarMediaId": "@lADPDgQ9rEI8hKHNBgDNBgM", "isMySelf": "true", "jobNumber": ""}}], "success": true}}