{"code": 200, "statusCode": 200, "body": {"source": "supplier", "uid": 6833116, "id": "SUP_1023A742034B213374E4000Q", "receiptorName": "123", "orgId": *********, "extension": {"BANK_EXT_INST_BRANCH_NAME": "杭州银行股份有限公司营业部", "BANK_EXT_INST_BRANCH_CODE": "123456", "BANK_EXT_ACCOUNT_TYPE": "TO_PUB", "BANK_EXT_INST_PROVINCE": "浙江省", "BANK_EXT_INST_CITY": "杭州市", "BANK_EXT_BANK_CODE": "************", "BANK_EXT_INST_CODE": "杭州银行", "BANK_EXT_INST_NAME": "杭州银行", "BANK_EXT_CUSTOM_BRANCH_CODE": "123", "BANK_EXT_CUSTOM_BRANCH_NAME": "杭州银行股份有限公司营业部111"}, "receiptorType": "BANKCARD", "receiptorId": "123"}, "responseText": {"data": {"id": "202008171909362470", "orgId": ********, "receiptorId": "***********", "receiptorName": "许凌志", "receiptorType": "ALIPAY", "uid": ********}, "success": true}}