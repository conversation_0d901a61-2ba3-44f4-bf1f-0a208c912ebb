{"code": 200, "body": [{"actionId": "G-ACT-101297681592212C1280000K", "actionInstanceId": "G-ACT-INST-1012881DA032212C12800007", "filters": [], "connectorInstanceId": "G-CONN-INST-1011E908C76E212B30F0000B", "connectorBizAlias": "COMMON", "inputRules": [{"rules": [{"srcDataId": "InnerContactField_1INCS3747D280/id", "type": "mapping", "dstGroupId": "inputs", "srcGroupId": "form", "dstDataId": "$/personId"}], "srcDataIdRef": "InnerContactField_1INCS3747D280", "extValue": "InnerContactField_1INCS3747D280", "dstDataIdRef": "root"}], "action": {"description": "员工基本信息（正式环境）", "status": "valid", "outputSchema": "{\"type\":\"object\",\"title\":\"R\",\"properties\":{\"result\":{\"type\":\"object\",\"title\":\"人员\",\"properties\":{\"companyCode\":{\"type\":\"string\",\"title\":\"公司代码\"},\"factoryCode\":{\"type\":\"string\",\"title\":\"档案分组代码\"},\"deptName\":{\"type\":\"string\",\"title\":\"部门名称\"},\"hireDate\":{\"type\":\"string\",\"title\":\"入职日期\"},\"hrCategory\":{\"type\":\"string\",\"title\":\"HR分类\"},\"companyName\":{\"type\":\"string\",\"title\":\"公司名称\"},\"factoryName\":{\"type\":\"string\",\"title\":\"档案分组\"},\"postName\":{\"type\":\"string\",\"title\":\"岗位名称\"},\"name\":{\"type\":\"string\",\"title\":\"姓名\"},\"postCode\":{\"type\":\"string\",\"title\":\"岗位代码\"},\"id\":{\"type\":\"string\",\"title\":\"工号\"},\"keyPosition\":{\"type\":\"string\",\"title\":\"是否关键岗位\"},\"deptCode\":{\"type\":\"string\",\"title\":\"部门代码\"}}},\"code\":{\"type\":\"number\",\"title\":\"返回代码\"},\"message\":{\"type\":\"string\",\"title\":\"返回信息\"}}}", "actionId": "G-ACT-101297681592212C1280000K", "versionId": "G-ACT-VER-1014B5C3C9760B8AD0D9000L", "versionNo": 7, "gmtCreate": 1613971592000, "gmtModified": 1623070853000, "visibleType": 0, "connectorId": "G-CONN-1011E908C76B212B30F0000P", "actionType": 1, "apiType": "http", "orgId": 1319129, "iconUrl": "https://static.dingtalk.com/media/lADPDetfTPEpi_HNAlLNAk4_590_594.jpg", "inputSchema": "{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"personId\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"钉钉ID\"}}}", "connectorInstId": "G-CONN-INST-1011E908C76E212B30F0000B", "info": {"async": false, "name": "员工基本信息", "description": "员工基本信息（正式环境）"}, "name": "员工基本信息", "content": "{\"childNode\":{\"childNode\":{\"name\":\"end\",\"nodeId\":\"3\",\"prevId\":\"2\",\"properties\":{\"outputSchema\":{\"type\":\"object\",\"title\":\"R\",\"properties\":{\"result\":{\"type\":\"object\",\"title\":\"人员\",\"properties\":{\"companyCode\":{\"type\":\"string\",\"title\":\"公司代码\"},\"factoryCode\":{\"type\":\"string\",\"title\":\"档案分组代码\"},\"deptName\":{\"type\":\"string\",\"title\":\"部门名称\"},\"hireDate\":{\"type\":\"string\",\"title\":\"入职日期\"},\"hrCategory\":{\"type\":\"string\",\"title\":\"HR分类\"},\"companyName\":{\"type\":\"string\",\"title\":\"公司名称\"},\"factoryName\":{\"type\":\"string\",\"title\":\"档案分组\"},\"postName\":{\"type\":\"string\",\"title\":\"岗位名称\"},\"name\":{\"type\":\"string\",\"title\":\"姓名\"},\"postCode\":{\"type\":\"string\",\"title\":\"岗位代码\"},\"id\":{\"type\":\"string\",\"title\":\"工号\"},\"keyPosition\":{\"type\":\"string\",\"title\":\"是否关键岗位\"},\"deptCode\":{\"type\":\"string\",\"title\":\"部门代码\"}}},\"code\":{\"type\":\"number\",\"title\":\"返回代码\"},\"message\":{\"type\":\"string\",\"title\":\"返回信息\"}}},\"rule\":[{\"func\":\"@(\\\"2/$\\\")\",\"type\":\"reference\",\"dest\":\"$\"}],\"type\":\"end\"},\"type\":\"endpoint\"},\"name\":\"员工基本信息\",\"nodeId\":\"2\",\"prevId\":\"1\",\"properties\":{\"endpoint\":{\"type\":\"http\",\"apiId\":11827},\"outputSchema\":{\"type\":\"object\",\"title\":\"R\",\"properties\":{\"result\":{\"type\":\"object\",\"title\":\"人员\",\"properties\":{\"companyCode\":{\"type\":\"string\",\"title\":\"公司代码\"},\"factoryCode\":{\"type\":\"string\",\"title\":\"档案分组代码\"},\"deptName\":{\"type\":\"string\",\"title\":\"部门名称\"},\"hireDate\":{\"type\":\"string\",\"title\":\"入职日期\"},\"hrCategory\":{\"type\":\"string\",\"title\":\"HR分类\"},\"companyName\":{\"type\":\"string\",\"title\":\"公司名称\"},\"factoryName\":{\"type\":\"string\",\"title\":\"档案分组\"},\"postName\":{\"type\":\"string\",\"title\":\"岗位名称\"},\"name\":{\"type\":\"string\",\"title\":\"姓名\"},\"postCode\":{\"type\":\"string\",\"title\":\"岗位代码\"},\"id\":{\"type\":\"string\",\"title\":\"工号\"},\"keyPosition\":{\"type\":\"string\",\"title\":\"是否关键岗位\"},\"deptCode\":{\"type\":\"string\",\"title\":\"部门代码\"}}},\"code\":{\"type\":\"number\",\"title\":\"返回代码\"},\"message\":{\"type\":\"string\",\"title\":\"返回信息\"}}},\"inputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"personId\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"钉钉ID\"}}},\"rule\":[{\"func\":\"@(\\\"1/$\\\")\",\"type\":\"reference\",\"dest\":\"$\"}],\"type\":\"serviceActivator\"},\"type\":\"endpoint\"},\"name\":\"start\",\"nodeId\":\"1\",\"properties\":{\"outputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"personId\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"钉钉ID\"}}},\"type\":\"start\"},\"type\":\"endpoint\"}"}, "connectorId": "G-CONN-1011E908C76B212B30F0000P", "versionId": "G-ACT-VER-1012CC80D47D2105A0E3000I", "target": {"appUuid": ""}, "outputRules": [{"rules": [{"srcDataId": "$/result/id", "type": "mapping", "dstGroupId": "form", "srcGroupId": "outputs", "dstDataId": "TextField_1P64XHR545SW0"}], "srcDataIdRef": "root", "extValue": "{\"componentId\":\"TextField_1P64XHR545SW0\",\"componentIdRef\":\"TextField_1P64XHR545SW0\"}", "dstDataIdRef": "TextField_1P64XHR545SW0"}, {"rules": [{"srcDataId": "$/result/deptName", "type": "mapping", "dstGroupId": "form", "srcGroupId": "outputs", "dstDataId": "TextField_1SCDYVHYM25C0"}], "srcDataIdRef": "root", "extValue": "{\"componentId\":\"TextField_1SCDYVHYM25C0\",\"componentIdRef\":\"TextField_1SCDYVHYM25C0\"}", "dstDataIdRef": "TextField_1SCDYVHYM25C0"}], "actionBizAlias": "COMMON"}, {"actionId": "G-ACT-101297681592212C1280000K", "actionInstanceId": "G-ACT-INST-10128829730C0B8AD0D90008", "filters": [], "connectorInstanceId": "G-CONN-INST-1011E908C76E212B30F0000B", "connectorBizAlias": "COMMON", "inputRules": [{"rules": [{"srcDataId": "InnerContactField_F7SHGP25ZVS0/id", "type": "mapping", "dstGroupId": "inputs", "srcGroupId": "form", "dstDataId": "$/personId"}], "srcDataIdRef": "InnerContactField_F7SHGP25ZVS0", "extValue": "InnerContactField_F7SHGP25ZVS0", "dstDataIdRef": "root"}], "action": {"description": "员工基本信息（正式环境）", "status": "valid", "outputSchema": "{\"type\":\"object\",\"title\":\"R\",\"properties\":{\"result\":{\"type\":\"object\",\"title\":\"人员\",\"properties\":{\"companyCode\":{\"type\":\"string\",\"title\":\"公司代码\"},\"factoryCode\":{\"type\":\"string\",\"title\":\"档案分组代码\"},\"deptName\":{\"type\":\"string\",\"title\":\"部门名称\"},\"hireDate\":{\"type\":\"string\",\"title\":\"入职日期\"},\"hrCategory\":{\"type\":\"string\",\"title\":\"HR分类\"},\"companyName\":{\"type\":\"string\",\"title\":\"公司名称\"},\"factoryName\":{\"type\":\"string\",\"title\":\"档案分组\"},\"postName\":{\"type\":\"string\",\"title\":\"岗位名称\"},\"name\":{\"type\":\"string\",\"title\":\"姓名\"},\"postCode\":{\"type\":\"string\",\"title\":\"岗位代码\"},\"id\":{\"type\":\"string\",\"title\":\"工号\"},\"keyPosition\":{\"type\":\"string\",\"title\":\"是否关键岗位\"},\"deptCode\":{\"type\":\"string\",\"title\":\"部门代码\"}}},\"code\":{\"type\":\"number\",\"title\":\"返回代码\"},\"message\":{\"type\":\"string\",\"title\":\"返回信息\"}}}", "actionId": "G-ACT-101297681592212C1280000K", "versionId": "G-ACT-VER-1014B5C3C9760B8AD0D9000L", "versionNo": 7, "gmtCreate": 1613971592000, "gmtModified": 1623070853000, "visibleType": 0, "connectorId": "G-CONN-1011E908C76B212B30F0000P", "actionType": 1, "apiType": "http", "orgId": 1319129, "iconUrl": "https://static.dingtalk.com/media/lADPDetfTPEpi_HNAlLNAk4_590_594.jpg", "inputSchema": "{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"personId\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"钉钉ID\"}}}", "connectorInstId": "G-CONN-INST-1011E908C76E212B30F0000B", "info": {"async": false, "name": "员工基本信息", "description": "员工基本信息（正式环境）"}, "name": "员工基本信息", "content": "{\"childNode\":{\"childNode\":{\"name\":\"end\",\"nodeId\":\"3\",\"prevId\":\"2\",\"properties\":{\"outputSchema\":{\"type\":\"object\",\"title\":\"R\",\"properties\":{\"result\":{\"type\":\"object\",\"title\":\"人员\",\"properties\":{\"companyCode\":{\"type\":\"string\",\"title\":\"公司代码\"},\"factoryCode\":{\"type\":\"string\",\"title\":\"档案分组代码\"},\"deptName\":{\"type\":\"string\",\"title\":\"部门名称\"},\"hireDate\":{\"type\":\"string\",\"title\":\"入职日期\"},\"hrCategory\":{\"type\":\"string\",\"title\":\"HR分类\"},\"companyName\":{\"type\":\"string\",\"title\":\"公司名称\"},\"factoryName\":{\"type\":\"string\",\"title\":\"档案分组\"},\"postName\":{\"type\":\"string\",\"title\":\"岗位名称\"},\"name\":{\"type\":\"string\",\"title\":\"姓名\"},\"postCode\":{\"type\":\"string\",\"title\":\"岗位代码\"},\"id\":{\"type\":\"string\",\"title\":\"工号\"},\"keyPosition\":{\"type\":\"string\",\"title\":\"是否关键岗位\"},\"deptCode\":{\"type\":\"string\",\"title\":\"部门代码\"}}},\"code\":{\"type\":\"number\",\"title\":\"返回代码\"},\"message\":{\"type\":\"string\",\"title\":\"返回信息\"}}},\"rule\":[{\"func\":\"@(\\\"2/$\\\")\",\"type\":\"reference\",\"dest\":\"$\"}],\"type\":\"end\"},\"type\":\"endpoint\"},\"name\":\"员工基本信息\",\"nodeId\":\"2\",\"prevId\":\"1\",\"properties\":{\"endpoint\":{\"type\":\"http\",\"apiId\":11827},\"outputSchema\":{\"type\":\"object\",\"title\":\"R\",\"properties\":{\"result\":{\"type\":\"object\",\"title\":\"人员\",\"properties\":{\"companyCode\":{\"type\":\"string\",\"title\":\"公司代码\"},\"factoryCode\":{\"type\":\"string\",\"title\":\"档案分组代码\"},\"deptName\":{\"type\":\"string\",\"title\":\"部门名称\"},\"hireDate\":{\"type\":\"string\",\"title\":\"入职日期\"},\"hrCategory\":{\"type\":\"string\",\"title\":\"HR分类\"},\"companyName\":{\"type\":\"string\",\"title\":\"公司名称\"},\"factoryName\":{\"type\":\"string\",\"title\":\"档案分组\"},\"postName\":{\"type\":\"string\",\"title\":\"岗位名称\"},\"name\":{\"type\":\"string\",\"title\":\"姓名\"},\"postCode\":{\"type\":\"string\",\"title\":\"岗位代码\"},\"id\":{\"type\":\"string\",\"title\":\"工号\"},\"keyPosition\":{\"type\":\"string\",\"title\":\"是否关键岗位\"},\"deptCode\":{\"type\":\"string\",\"title\":\"部门代码\"}}},\"code\":{\"type\":\"number\",\"title\":\"返回代码\"},\"message\":{\"type\":\"string\",\"title\":\"返回信息\"}}},\"inputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"personId\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"钉钉ID\"}}},\"rule\":[{\"func\":\"@(\\\"1/$\\\")\",\"type\":\"reference\",\"dest\":\"$\"}],\"type\":\"serviceActivator\"},\"type\":\"endpoint\"},\"name\":\"start\",\"nodeId\":\"1\",\"properties\":{\"outputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"personId\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"钉钉ID\"}}},\"type\":\"start\"},\"type\":\"endpoint\"}"}, "connectorId": "G-CONN-1011E908C76B212B30F0000P", "versionId": "G-ACT-VER-1012CC80D47D2105A0E3000I", "target": {"appUuid": ""}, "outputRules": [{"rules": [{"srcDataId": "$/result/id", "type": "mapping", "dstGroupId": "form", "srcGroupId": "outputs", "dstDataId": "TextField_QFSI63COG340"}], "srcDataIdRef": "root", "extValue": "{\"componentId\":\"TextField_QFSI63COG340\",\"componentIdRef\":\"TextField_QFSI63COG340\"}", "dstDataIdRef": "TextField_QFSI63COG340"}], "actionBizAlias": "COMMON"}, {"actionId": "G-ACT-1013327F675B212C2658000K", "actionInstanceId": "G-ACT-INST-10134C14E3150B15509F000M", "filters": [], "connectorInstanceId": "G-CONN-INST-1012CBBFB28E2105A0E3000I", "connectorBizAlias": "FORM", "inputRules": [], "action": {"description": "筛选表单", "status": "valid", "outputSchema": "{\"type\":\"string\",\"title\":\"root\"}", "actionId": "G-ACT-1013327F675B212C2658000K", "versionId": "G-ACT-VER-1013B8AFEA6B2105A0E30003", "versionNo": 3, "gmtCreate": 1616573589000, "gmtModified": 1618824915000, "visibleType": 0, "connectorId": "G-CONN-1012C699A4AD212B332D0007", "actionType": 1, "apiType": "http", "orgId": 1319129, "iconUrl": "https://static.dingtalk.com/media/lALPDeC20j7GVrPNASzNASw_300_300.png", "inputSchema": "{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"param_3b59\":{\"x-dd-position\":\"BODY\",\"type\":\"string\",\"title\":\"mockedParam\"}}}", "connectorInstId": "G-CONN-INST-1012CBBFB28E2105A0E3000I", "info": {"name": "筛选", "description": "筛选表单"}, "name": "筛选", "content": "{\"childNode\":{\"childNode\":{\"name\":\"end\",\"nodeId\":\"3\",\"prevId\":\"2\",\"properties\":{\"outputSchema\":{\"type\":\"string\",\"title\":\"root\"},\"rule\":[{\"func\":\"@(\\\"2/$\\\")\",\"type\":\"reference\",\"dest\":\"$\"}],\"type\":\"end\"},\"type\":\"endpoint\"},\"name\":\"筛选\",\"nodeId\":\"2\",\"prevId\":\"1\",\"properties\":{\"endpoint\":{\"type\":\"http\",\"apiId\":9223},\"outputSchema\":{\"type\":\"string\",\"title\":\"root\"},\"inputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"param_3b59\":{\"x-dd-position\":\"BODY\",\"type\":\"string\",\"title\":\"mockedParam\"}}},\"rule\":[{\"func\":\"@(\\\"1/$\\\")\",\"type\":\"reference\",\"dest\":\"$\"}],\"type\":\"serviceActivator\"},\"type\":\"endpoint\"},\"name\":\"start\",\"nodeId\":\"1\",\"properties\":{\"outputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"param_3b59\":{\"x-dd-position\":\"BODY\",\"type\":\"string\",\"title\":\"mockedParam\"}}},\"type\":\"start\"},\"type\":\"endpoint\"}"}, "connectorId": "G-CONN-1012C699A4AD212B332D0007", "versionId": "G-ACT-VER-1013B8AFEA6B2105A0E30003", "target": {"appUuid": "SWAPP-B4A8FE580F6FFCA439A2ACFFB6E7B95C", "formCode": "PROC-1B27825B-D7CC-48A8-87B5-DC1CC88F851D"}, "outputRules": [{"rules": [{"srcDataId": "TextField_228NZL01TBGG0", "type": "mapping", "dstGroupId": "form", "srcGroupId": "form", "dstDataId": "TableField_8HFL88PIINK0/DDSelectField_1HBUK4FJO7EO0/options[*]/key"}], "srcDataIdRef": "TextField_228NZL01TBGG0", "extValue": "{\"componentId\":\"DDSelectField_1HBUK4FJO7EO0\",\"componentIdRef\":\"TableField_8HFL88PIINK0/DDSelectField_1HBUK4FJO7EO0/options[*]/key\"}", "dstDataIdRef": "DDSelectField_1HBUK4FJO7EO0"}, {"rules": [{"srcDataId": "TextField_XACN2JJ9UEO0", "type": "mapping", "dstGroupId": "form", "srcGroupId": "form", "dstDataId": "TableField_8HFL88PIINK0/DDSelectField_1HBUK4FJO7EO0/options[*]/value"}], "srcDataIdRef": "TextField_XACN2JJ9UEO0", "extValue": "{\"componentId\":\"DDSelectField_1HBUK4FJO7EO0\",\"componentIdRef\":\"TableField_8HFL88PIINK0/DDSelectField_1HBUK4FJO7EO0/options[*]/value\"}", "dstDataIdRef": "DDSelectField_1HBUK4FJO7EO0"}], "actionBizAlias": "SELECT"}, {"actionId": "G-ACT-1014243D1691212B332E000F", "actionInstanceId": "G-ACT-INST-10134C330F590B15509F000M", "filters": [], "connectorInstanceId": "G-CONN-INST-10129832303C2107068E000L", "connectorBizAlias": "COMMON", "inputRules": [{"rules": [{"srcDataId": "DDSelectField_1H16QV5VCRPC0/id", "type": "mapping", "dstGroupId": "inputs", "srcGroupId": "form", "dstDataId": "$/companyCode"}], "srcDataIdRef": "DDSelectField_1H16QV5VCRPC0", "extValue": "DDSelectField_1H16QV5VCRPC0", "dstDataIdRef": "root"}, {"rules": [{"srcDataId": "TableField_8HFL88PIINK0/DDSelectField_12GD6ED3H1400/id", "type": "mapping", "dstGroupId": "inputs", "srcGroupId": "form", "dstDataId": "$/costCode"}], "srcDataIdRef": "DDSelectField_12GD6ED3H1400", "extValue": "DDSelectField_12GD6ED3H1400", "dstDataIdRef": "root"}, {"rules": [{"srcData": "dev", "srcDataId": "", "type": "fixed", "dstGroupId": "inputs", "srcGroupId": "", "dstDataId": "$/system"}], "srcDataIdRef": "", "extValue": "", "dstDataIdRef": "root"}, {"rules": [{"srcData": "po", "srcDataId": "", "type": "fixed", "dstGroupId": "inputs", "srcGroupId": "", "dstDataId": "$/inferfaceType"}], "srcDataIdRef": "", "extValue": "", "dstDataIdRef": "root"}], "action": {"description": "动态查询SAP业务范围", "status": "valid", "outputSchema": "{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"result\":{\"type\":\"array\",\"title\":\"result\",\"items\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"itemName\":{\"type\":\"string\",\"title\":\"itemName\"},\"itemCode\":{\"type\":\"string\",\"title\":\"itemCode\"}}}},\"code\":{\"type\":\"number\",\"title\":\"code\"},\"message\":{\"type\":\"string\",\"title\":\"message\"}}}", "actionId": "G-ACT-1014243D1691212B332E000F", "versionId": "G-ACT-VER-1014527241732127F959000T", "versionNo": 9, "gmtCreate": 1620629329000, "gmtModified": 1621404565000, "visibleType": 0, "connectorId": "G-CONN-10129832303A2107068E0003", "actionType": 1, "apiType": "http", "orgId": 1319129, "iconUrl": "https://static.dingtalk.com/media/lADPDeC20_fMpvHNAlLNAk4_590_594.jpg", "inputSchema": "{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"companyCode\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"companyCode\"},\"costCode\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"costCode\"},\"system\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"system\"},\"inferfaceType\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"inferfaceType\"}}}", "connectorInstId": "G-CONN-INST-10129832303C2107068E000L", "info": {"async": false, "name": "动态查询SAP业务范围", "description": "动态查询SAP业务范围"}, "name": "动态查询SAP业务范围", "content": "{\"childNode\":{\"childNode\":{\"name\":\"end\",\"nodeId\":\"3\",\"prevId\":\"2\",\"properties\":{\"outputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"result\":{\"type\":\"array\",\"title\":\"result\",\"items\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"itemName\":{\"type\":\"string\",\"title\":\"itemName\"},\"itemCode\":{\"type\":\"string\",\"title\":\"itemCode\"}}}},\"code\":{\"type\":\"number\",\"title\":\"code\"},\"message\":{\"type\":\"string\",\"title\":\"message\"}}},\"rule\":[{\"func\":\"@(\\\"2/$\\\")\",\"type\":\"reference\",\"dest\":\"$\"}],\"type\":\"end\"},\"type\":\"endpoint\"},\"name\":\"动态查询SAP业务范围\",\"nodeId\":\"2\",\"prevId\":\"1\",\"properties\":{\"endpoint\":{\"type\":\"http\",\"apiId\":10746},\"outputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"result\":{\"type\":\"array\",\"title\":\"result\",\"items\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"itemName\":{\"type\":\"string\",\"title\":\"itemName\"},\"itemCode\":{\"type\":\"string\",\"title\":\"itemCode\"}}}},\"code\":{\"type\":\"number\",\"title\":\"code\"},\"message\":{\"type\":\"string\",\"title\":\"message\"}}},\"inputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"companyCode\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"companyCode\"},\"costCode\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"costCode\"},\"system\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"system\"},\"inferfaceType\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"inferfaceType\"}}},\"rule\":[{\"func\":\"@(\\\"1/$\\\")\",\"type\":\"reference\",\"dest\":\"$\"}],\"type\":\"serviceActivator\"},\"type\":\"endpoint\"},\"name\":\"start\",\"nodeId\":\"1\",\"properties\":{\"outputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"companyCode\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"companyCode\"},\"costCode\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"costCode\"},\"system\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"system\"},\"inferfaceType\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"inferfaceType\"}}},\"type\":\"start\"},\"type\":\"endpoint\"}"}, "connectorId": "G-CONN-10129832303A2107068E0003", "versionId": "G-ACT-VER-1014527241732127F959000T", "target": {"appUuid": ""}, "outputRules": [{"rules": [{"srcDataId": "$/result[*]/itemName", "type": "mapping", "dstGroupId": "form", "srcGroupId": "outputs", "dstDataId": "TableField_8HFL88PIINK0/DDSelectField_1XEIS9ORD7400/options[*]/value"}, {"srcDataId": "$/result[*]/itemCode", "type": "mapping", "dstGroupId": "form", "srcGroupId": "outputs", "dstDataId": "TableField_8HFL88PIINK0/DDSelectField_1XEIS9ORD7400/options[*]/key"}], "srcDataIdRef": "root", "extValue": "{\"componentId\":\"DDSelectField_1XEIS9ORD7400\",\"componentIdRef\":\"TableField_8HFL88PIINK0/DDSelectField_1XEIS9ORD7400/options[*]/value\"}", "dstDataIdRef": "DDSelectField_1XEIS9ORD7400"}], "actionBizAlias": "COMMON"}, {"actionId": "G-ACT-1014286DF9D0212C0D65000X", "actionInstanceId": "G-ACT-INST-10134C2426250B15509F000Q", "filters": [], "connectorInstanceId": "G-CONN-INST-10129832303C2107068E000L", "connectorBizAlias": "COMMON", "inputRules": [{"rules": [{"srcDataId": "DDSelectField_1H16QV5VCRPC0/id", "type": "mapping", "dstGroupId": "inputs", "srcGroupId": "form", "dstDataId": "$/companyCode"}], "srcDataIdRef": "DDSelectField_1H16QV5VCRPC0", "extValue": "DDSelectField_1H16QV5VCRPC0", "dstDataIdRef": "root"}, {"rules": [{"srcData": "po", "srcDataId": "", "type": "fixed", "dstGroupId": "inputs", "srcGroupId": "", "dstDataId": "$/inferfaceType"}], "srcDataIdRef": "", "extValue": "", "dstDataIdRef": "root"}, {"rules": [{"srcData": "dev", "srcDataId": "", "type": "fixed", "dstGroupId": "inputs", "srcGroupId": "", "dstDataId": "$/system"}], "srcDataIdRef": "", "extValue": "", "dstDataIdRef": "root"}], "action": {"description": "动态查询SAP部门和成本中心", "status": "valid", "outputSchema": "{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"result\":{\"type\":\"object\",\"title\":\"result\",\"properties\":{\"costCenter\":{\"type\":\"array\",\"title\":\"costCenter\",\"items\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"companyCode\":{\"type\":\"string\",\"title\":\"companyCode\"},\"costCenter\":{\"type\":\"string\",\"title\":\"costCenter\"},\"costCenterName\":{\"type\":\"string\",\"title\":\"costCenterName\"},\"codeAndName\":{\"type\":\"string\",\"title\":\"codeAndName\"}}}},\"dept\":{\"type\":\"array\",\"title\":\"dept\",\"items\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"deptName\":{\"type\":\"string\",\"title\":\"deptName\"},\"deptCode\":{\"type\":\"string\",\"title\":\"deptCode\"}}}}}},\"code\":{\"type\":\"number\",\"title\":\"code\"},\"message\":{\"type\":\"string\",\"title\":\"message\"}}}", "actionId": "G-ACT-1014286DF9D0212C0D65000X", "versionId": "G-ACT-VER-1014527197BE212B332D000S", "versionNo": 4, "gmtCreate": 1620699642000, "gmtModified": 1621404522000, "visibleType": 0, "connectorId": "G-CONN-10129832303A2107068E0003", "actionType": 1, "apiType": "http", "orgId": 1319129, "iconUrl": "https://static.dingtalk.com/media/lADPDetfT-4o6QTNAlLNAk4_590_594.jpg", "inputSchema": "{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"companyCode\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"companyCode\"},\"system\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"system\"},\"inferfaceType\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"inferfaceType\"}}}", "connectorInstId": "G-CONN-INST-10129832303C2107068E000L", "info": {"async": false, "name": "动态查询SAP部门和成本中心", "description": "动态查询SAP部门和成本中心"}, "name": "动态查询SAP部门和成本中心", "content": "{\"childNode\":{\"childNode\":{\"name\":\"end\",\"nodeId\":\"3\",\"prevId\":\"2\",\"properties\":{\"outputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"result\":{\"type\":\"object\",\"title\":\"result\",\"properties\":{\"costCenter\":{\"type\":\"array\",\"title\":\"costCenter\",\"items\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"companyCode\":{\"type\":\"string\",\"title\":\"companyCode\"},\"costCenter\":{\"type\":\"string\",\"title\":\"costCenter\"},\"costCenterName\":{\"type\":\"string\",\"title\":\"costCenterName\"},\"codeAndName\":{\"type\":\"string\",\"title\":\"codeAndName\"}}}},\"dept\":{\"type\":\"array\",\"title\":\"dept\",\"items\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"deptName\":{\"type\":\"string\",\"title\":\"deptName\"},\"deptCode\":{\"type\":\"string\",\"title\":\"deptCode\"}}}}}},\"code\":{\"type\":\"number\",\"title\":\"code\"},\"message\":{\"type\":\"string\",\"title\":\"message\"}}},\"rule\":[{\"func\":\"@(\\\"2/$\\\")\",\"type\":\"reference\",\"dest\":\"$\"}],\"type\":\"end\"},\"type\":\"endpoint\"},\"name\":\"动态查询SAP部门和成本中心\",\"nodeId\":\"2\",\"prevId\":\"1\",\"properties\":{\"endpoint\":{\"type\":\"http\",\"apiId\":10745},\"outputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"result\":{\"type\":\"object\",\"title\":\"result\",\"properties\":{\"costCenter\":{\"type\":\"array\",\"title\":\"costCenter\",\"items\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"companyCode\":{\"type\":\"string\",\"title\":\"companyCode\"},\"costCenter\":{\"type\":\"string\",\"title\":\"costCenter\"},\"costCenterName\":{\"type\":\"string\",\"title\":\"costCenterName\"},\"codeAndName\":{\"type\":\"string\",\"title\":\"codeAndName\"}}}},\"dept\":{\"type\":\"array\",\"title\":\"dept\",\"items\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"deptName\":{\"type\":\"string\",\"title\":\"deptName\"},\"deptCode\":{\"type\":\"string\",\"title\":\"deptCode\"}}}}}},\"code\":{\"type\":\"number\",\"title\":\"code\"},\"message\":{\"type\":\"string\",\"title\":\"message\"}}},\"inputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"companyCode\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"companyCode\"},\"system\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"system\"},\"inferfaceType\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"inferfaceType\"}}},\"rule\":[{\"func\":\"@(\\\"1/$\\\")\",\"type\":\"reference\",\"dest\":\"$\"}],\"type\":\"serviceActivator\"},\"type\":\"endpoint\"},\"name\":\"start\",\"nodeId\":\"1\",\"properties\":{\"outputSchema\":{\"type\":\"object\",\"title\":\"root\",\"properties\":{\"companyCode\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"companyCode\"},\"system\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"system\"},\"inferfaceType\":{\"x-dd-position\":\"QUERY\",\"type\":\"string\",\"title\":\"inferfaceType\"}}},\"type\":\"start\"},\"type\":\"endpoint\"}"}, "connectorId": "G-CONN-10129832303A2107068E0003", "versionId": "G-ACT-VER-1014527197BE212B332D000S", "target": {"appUuid": ""}, "outputRules": [{"rules": [{"srcDataId": "$/result/dept[*]/deptName", "type": "mapping", "dstGroupId": "form", "srcGroupId": "outputs", "dstDataId": "DDSelectField_1EYAZYELCAG00/options[*]/value"}, {"srcDataId": "$/result/dept[*]/deptCode", "type": "mapping", "dstGroupId": "form", "srcGroupId": "outputs", "dstDataId": "DDSelectField_1EYAZYELCAG00/options[*]/key"}], "srcDataIdRef": "root", "extValue": "{\"componentId\":\"DDSelectField_1EYAZYELCAG00\",\"componentIdRef\":\"DDSelectField_1EYAZYELCAG00/options[*]/value\"}", "dstDataIdRef": "DDSelectField_1EYAZYELCAG00"}, {"rules": [{"srcDataId": "$/result/costCenter[*]/codeAndName", "type": "mapping", "dstGroupId": "form", "srcGroupId": "outputs", "dstDataId": "TableField_8HFL88PIINK0/DDSelectField_12GD6ED3H1400/options[*]/value"}, {"srcDataId": "$/result/costCenter[*]/costCenter", "type": "mapping", "dstGroupId": "form", "srcGroupId": "outputs", "dstDataId": "TableField_8HFL88PIINK0/DDSelectField_12GD6ED3H1400/options[*]/key"}], "srcDataIdRef": "root", "extValue": "{\"componentId\":\"DDSelectField_12GD6ED3H1400\",\"componentIdRef\":\"TableField_8HFL88PIINK0/DDSelectField_12GD6ED3H1400/options[*]/value\"}", "dstDataIdRef": "DDSelectField_12GD6ED3H1400"}], "actionBizAlias": "COMMON"}]}