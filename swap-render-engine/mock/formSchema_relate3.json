[{"__ctx": {"idx": 0, "swapKey": "SeqNumberField_17KJ1YD7TO9S0", "swapPath": "/0", "parentKey": null}, "componentName": "SeqNumberField", "props": {"rule": [{"type": "common", "value": "KP"}, {"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"length": 8, "start": 1, "reset": "year"}}], "bizAlias": "invoice_apply_no", "id": "SeqNumberField_17KJ1YD7TO9S0", "label": "开票申请编号", "required": true}}, {"__ctx": {"idx": 0, "swapKey": "FormRelateField_1RZO0GHFGO740", "swapPath": "/1", "parentKey": null}, "componentName": "FormRelateField", "props": {"quote": 1, "bizAlias": "invoice_apply_related_customer", "label": "客户", "id": "FormRelateField_1RZO0GHFGO740", "title": "", "fields": [{"componentName": "TextField", "props": {"bizAlias": "customer_name", "placeholder": "请输入组织全称", "label": "客户名称", "id": "TextField-K2U5DHAA", "required": true}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "SWAPP-FA649D8CDD1427590DFD301B0FE9F765", "bizType": "crm_customer", "formCode": "PROC-1FC77E42-43C7-4A29-9D47-C3D7A348C69F", "appType": 1}}, "required": true, "multi": 0}}, {"__ctx": {"idx": 0, "swapKey": "DDBizSuite_127KIMRJZSK00", "swapPath": "/2", "parentKey": null}, "children": [{"__ctx": {"idx": 0, "swapKey": "FormRelateField_23G65NCJR0YO0", "swapPath": "/2/children/0", "parentKey": "DDBizSuite_127KIMRJZSK00"}, "componentName": "FormRelateField", "props": {"quote": 0, "hidden": false, "rely": {"formula": "", "fields": ["FormRelateField_1RZO0GHFGO740"], "type": "rely"}, "bizAlias": "payment_related_contract", "isRelateForm": true, "label": "合同", "id": "FormRelateField_23G65NCJR0YO0", "fields": [], "dataSource": {"params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "FormRelateField_1RZO0GHFGO740", "fieldId": "FormRelateField-K2U5O2WK#data_id"}]}, "target": {"appUuid": "SWAPP-FA649D8CDD1427590DFD301B0FE9F765", "bizType": "crm_contract", "formCode": "PROC-212F4A9F-5EBF-40C5-82B9-BBD11D4BBF53", "appType": 0}}, "required": true}}, {"__ctx": {"idx": 0, "swapKey": "FormRelateField_1R5RENUV3TFK0", "swapPath": "/2/children/1", "parentKey": "DDBizSuite_127KIMRJZSK00"}, "componentName": "FormRelateField", "props": {"quote": 0, "hidden": false, "rely": {"formula": "", "fields": [], "type": "rely"}, "bizAlias": "payment_related_order", "isRelateForm": true, "label": "订单", "id": "FormRelateField_1R5RENUV3TFK0", "fields": [], "dataSource": {"params": {"filters": []}, "target": {"appUuid": "SWAPP-FA649D8CDD1427590DFD301B0FE9F765", "bizType": "", "formCode": "PROC-F041934B-0DFB-40D4-9124-7DDE1D38A87A", "appType": 0}}, "required": false}}], "componentName": "DDBizSuite", "props": {"isPayment": false, "bizType": "contract_order_kit", "extract": true, "contarct": [], "bizAlias": "contract_order_kit", "id": "DDBizSuite_127KIMRJZSK00", "order": []}}, {"__ctx": {"idx": 0, "swapKey": "MoneyField_AV1BDQAFU8W0", "swapPath": "/3", "parentKey": null}, "componentName": "MoneyField", "props": {"notUpper": "0", "bizAlias": "invoice_apply_money", "label": "开票金额（元）", "placeholder": "请输入金额", "id": "MoneyField_AV1BDQAFU8W0", "required": true}}, {"__ctx": {"idx": 0, "swapKey": "DDSelectField_13D33P2WI56O0", "swapPath": "/4", "parentKey": null}, "componentName": "DDSelectField", "props": {"options": [{"value": "增值税普通发票", "key": "option_0"}, {"value": "增值税专用发票", "key": "option_1"}, {"value": "通用机打发票", "key": "option_2"}, {"extension": {"image": ""}, "value": "其他", "key": "option_1LNSOIR53UU80"}], "bizAlias": "invoice_apply_type", "label": "票据类型", "placeholder": "请选择", "id": "DDSelectField_13D33P2WI56O0", "required": false, "spread": false, "ratio": 50, "behaviorLinkage": []}}, {"__ctx": {"idx": 0, "swapKey": "FormRelateField_Q9M9RUZQH8G0", "swapPath": "/5", "parentKey": null}, "componentName": "FormRelateField", "props": {"quote": 1, "bizAlias": "", "label": "客户开票信息", "id": "FormRelateField_Q9M9RUZQH8G0", "title": "", "fields": [{"componentName": "TextField", "props": {"bizAlias": "", "label": "发票抬头", "placeholder": "请输入", "id": "TextField_1KFURAXTHB400", "required": true, "ratio": 50}}, {"componentName": "TextField", "props": {"bizAlias": "", "label": "纳税识别号", "placeholder": "请输入", "id": "TextField_1Q3L1OJUNU9S0", "required": true, "ratio": 50}}, {"componentName": "TextField", "props": {"bizAlias": "", "label": "开户银行", "placeholder": "请输入", "id": "TextField_20DLJW678C3K0", "required": true, "ratio": 50}}, {"componentName": "NumberField", "props": {"bizAlias": "", "label": "银行账号", "placeholder": "请输入数字", "id": "NumberField_1KBHKRA40L1C", "required": true, "ratio": 50}}, {"componentName": "TextareaField", "props": {"bizAlias": "", "label": "开票地址", "placeholder": "请输入", "id": "TextareaField_MSLVIXFT7F40", "required": false}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "SWAPP-FA649D8CDD1427590DFD301B0FE9F765", "bizType": "crm_invoice_info", "formCode": "PROC-9A528F45-34A7-4E32-BED6-C746AC6EF285", "appType": 1}}, "required": true, "multi": 0}}, {"__ctx": {"idx": 0, "swapKey": "TextareaField_M82WIRMVJXC0", "swapPath": "/6", "parentKey": null}, "componentName": "TextareaField", "props": {"bizAlias": "", "label": "备注", "placeholder": "请输入", "id": "TextareaField_M82WIRMVJXC0", "required": false}}, {"__ctx": {"idx": 0, "swapKey": "FormRelateField_PIU0HIFTIAO0", "swapPath": "/7", "parentKey": null}, "componentName": "FormRelateField", "props": {"quote": 1, "rely": {"formula": "", "fields": ["FormRelateField_1RZO0GHFGO740"], "type": "rely"}, "bizAlias": "", "label": "合同", "id": "FormRelateField_PIU0HIFTIAO0", "title": "", "fields": [], "dataSource": {"type": "form", "params": {"filter": "", "filters": [{"valueType": "current", "filterType": "EQ", "value": "FormRelateField_1RZO0GHFGO740", "fieldId": "FormRelateField-K2U5O2WK#data_id"}]}, "target": {"appUuid": "SWAPP-FA649D8CDD1427590DFD301B0FE9F765", "bizType": "crm_contract", "formCode": "PROC-212F4A9F-5EBF-40C5-82B9-BBD11D4BBF53", "appType": 0}}, "required": false, "multi": 0}}]