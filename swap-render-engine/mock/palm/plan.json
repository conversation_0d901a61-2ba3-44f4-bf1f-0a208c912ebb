[{"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 5, "reset": "day"}}], "bizAlias": "task_order_no", "label": "生产单号", "id": "SeqNumberField_1TUA06RKDCIO0"}}, {"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "sell_order", "sortFields": [], "label": "销售订单", "id": "FormRelateField_WARX43YYDOG0", "title": "", "fields": [{"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}], "bizAlias": "order_no", "label": "订单编号", "id": "SeqNumberField_1Y0WILAE4PB40"}}, {"componentName": "FormRelateField", "props": {"quote": 1, "bizAlias": "customer", "label": "客户", "id": "FormRelateField_T4NPF3AGFWG0", "title": "", "fields": [{"componentName": "TextField", "props": {"bizAlias": "customer_name", "placeholder": "请输入组织全称", "label": "客户名称", "id": "TextField-K2U5DHAA", "required": true}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "SWAPP-9A1BEE60DA71EF3E67F7BBE21B02E273", "bizType": "crm_customer", "formCode": "PROC-357194B7-096D-4038-B25C-89C71EF2CCF7", "appType": 1}}, "required": false, "multi": 0}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "", "bizType": "", "formCode": "PROC-B9D15BB6-E949-41E8-B9A7-CB7C00A5FE19", "appType": 0}}, "required": false, "multi": 0}}, {"componentName": "FormRelateField", "props": {"quote": 0, "bizAlias": "task_order_material", "label": "物料", "id": "FormRelateField_WL1UKA3FB340", "title": "", "fields": [{"componentName": "SeqNumberField", "props": {"rule": [{"type": "date", "value": "YYYYMMDD"}, {"type": "counter", "value": {"start": 1, "length": 8, "reset": "year"}}], "bizAlias": "material_no", "label": "物料编号", "id": "SeqNumberField_NSBF6SAX2SG0"}}, {"componentName": "TextField", "props": {"bizAlias": "material_name", "label": "名称", "placeholder": "请输入", "id": "TextField_K8BWAVZ9V0W0", "required": true, "ratio": 50}}, {"componentName": "TextField", "props": {"bizAlias": "unit", "label": "计量单位", "placeholder": "请输入", "id": "TextField_4WMFE6MF7EE0", "required": false, "ratio": 50}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "ding4f7815e99eeac513ffe93478753d9884", "bizType": "dingtalk.palmmanufacture.material", "formCode": "PROC-12B90453-6E33-486D-B776-CE2328BB9705", "appType": 1}}, "required": true, "multi": 0}}, {"componentName": "NumberField", "props": {"bizAlias": "plan_quantity", "label": "计划数量", "placeholder": "请输入数字", "id": "NumberField_MU6UWHWWCTC", "required": true, "ratio": 50}}, {"componentName": "DDDateRangeField", "props": {"duration": false, "unit": "天", "format": "yyyy-MM-dd", "bizAlias": "plan_date", "label": ["预计开始时间", "预计结束时间"], "placeholder": "请选择", "id": "DDDateRangeField_18DMPMF7QIWW0", "durationLabel": "时长", "required": true}}, {"componentName": "DDMultiSelectField", "props": {"version": "2.0", "rely": {"type": "async"}, "options": [], "bizAlias": "working_process", "label": "工序2.0版本", "placeholder": "请选择", "id": "DDMultiSelectField_23HE2LAXIIYO1", "fields": [{"props": {"label": "名称", "id": "TextField_5K9XZU623EW0"}}], "dataSource": {"type": "form", "params": {"filters": []}, "target": {"appUuid": "ding4f7815e99eeac513ffe93478753d9884", "formCode": "PROC-2F6E3B66-7F2C-4ABF-8522-0CB104BEB9A7"}}, "required": true, "spread": false, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "DDMultiSelectField", "componentOptions": "[]", "props": {"rely": {"type": "async"}, "options": [], "bizAlias": "task_order_process", "label": "工序", "placeholder": "请选择", "id": "DDMultiSelectField_23HE2LAXIIYO0", "fields": [{"props": {"label": "名称", "id": "TextField_5K9XZU623EW0"}}], "dataSource": {"type": "form", "params": {"filters": []}, "target": {"appUuid": "ding4f7815e99eeac513ffe93478753d9884", "formCode": "PROC-2F6E3B66-7F2C-4ABF-8522-0CB104BEB9A7"}}, "required": true, "spread": false, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "FormRelateField", "props": {"visible": false, "quote": 1, "bizAlias": "task_order_route", "label": "工艺路线", "id": "FormRelateField_9XDTCIO5EU80", "title": "", "fields": [], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "ding4f7815e99eeac513ffe93478753d9884", "bizType": "dingtalk.palmmanufacture.route", "formCode": "PROC-9D60C1EA-A3D7-4E78-96C4-067034666A96", "appType": 1}}, "required": false, "multi": 0}}, {"componentName": "DDDateRangeField", "props": {"duration": false, "unit": "天", "visible": false, "format": "yyyy-MM-dd", "bizAlias": "actual_date", "label": ["实际开始时间", "实际结束时间"], "placeholder": "请选择", "id": "DDDateRangeField_13ULKFXHPVK00", "durationLabel": "时长", "required": false}}, {"componentName": "DDSelectField", "componentOptions": "[{\"value\":\"未启动\",\"key\":\"option_0\"},{\"extension\":{\"image\":\"\"},\"value\":\"进行中\",\"key\":\"option_IKK5VVWEA7S0\"},{\"extension\":{\"image\":\"\"},\"value\":\"已结束\",\"key\":\"option_BU6VGZSV3400\"}]", "props": {"visible": false, "defaultValue": "未启动", "bizAlias": "status", "label": "生产状态", "required": false, "spread": false, "rely": {}, "options": [{"value": "未启动", "key": "option_0"}, {"extension": {"image": ""}, "value": "进行中", "key": "option_IKK5VVWEA7S0"}, {"extension": {"image": ""}, "value": "已结束", "key": "option_BU6VGZSV3400"}], "placeholder": "请选择", "id": "DDSelectField_1RLAHHXFLDKW0", "fields": [], "dataSource": {}, "ratio": 50, "behaviorLinkage": []}}, {"componentName": "FormRelateField", "props": {"visible": false, "quote": 1, "bizAlias": "", "label": "班组", "id": "FormRelateField_1XW2MN66CJB40", "title": "", "fields": [{"componentName": "TextField", "props": {"bizAlias": "team_name", "label": "班组名称", "placeholder": "请输入", "id": "TextField_12TIE95G66740", "required": true, "ratio": 50}}], "dataSource": {"type": "form", "params": {"filter": ""}, "target": {"appUuid": "ding4f7815e99eeac513ffe93478753d9884", "bizType": "dingtalk.palmmanufacture.team", "formCode": "PROC-8393ACC9-4E3B-4E19-B5F7-4AF9233AA774", "appType": 1}}, "required": false, "multi": 0}}, {"componentName": "TextField", "props": {"visible": false, "rely": {"formula": "", "type": "rely", "fields": ["FormRelateField_WL1UKA3FB340"], "behavior": "readonly"}, "defaultValue": "", "notPrint": "1", "bizAlias": "hidden_material", "label": "物料名称（隐藏）", "placeholder": "请输入", "id": "TextField_J83JLV98OQ00", "fields": [{"props": {"id": "TextField_K8BWAVZ9V0W0"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "FormRelateField_WL1UKA3FB340", "fieldId": "id"}]}, "target": {"appUuid": "ding4f7815e99eeac513ffe93478753d9884", "formCode": "PROC-12B90453-6E33-486D-B776-CE2328BB9705"}}, "required": false, "ratio": 50}}, {"componentName": "DDDateField", "props": {"visible": false, "defaultValue": "", "format": "yyyy-MM-dd", "bizAlias": "hidden_date", "label": "当前日期（隐藏）", "required": false, "unit": "天", "rely": {"formula": "TODAY()", "type": "formula", "fields": [], "version": 2}, "notPrint": "1", "placeholder": "请选择", "id": "DDDateField_1I82D78D4YOW0", "fields": [], "dataSource": {}}}]