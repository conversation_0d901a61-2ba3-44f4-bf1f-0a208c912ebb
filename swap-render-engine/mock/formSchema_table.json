[{"componentName": "DDSelectField", "props": {"rely": {"type": "async"}, "options": [], "label": "项目名称", "placeholder": "请选择", "id": "DDSelectField_3YTT0PJ1ZC60", "fields": [{"suiteId": "", "hidden": false, "children": [], "opList": [], "dataType": "String", "suiteBizType": "", "suiteBizAlias": "", "componentName": "TextField", "displayPosition": "", "formField": true, "props": {"bizType": "", "statField": [], "link": "", "align": "", "durationLabel": "", "required": true, "content": "", "holidayOptions": [], "quote": "", "options": [], "id": "TextField_LWO7JZM7IV40", "displayGroupBy": "", "placeHolder": "请输入", "behaviorLinkage": [], "objOptions": [], "notUpper": "", "format": "", "bizAlias": "", "label": "项目名称", "unit": "", "mainTitle": "", "notPrint": "", "fields": [], "attendTypeLabel": "", "actionName": ""}}], "dataSource": {"type": "form", "params": {"filters": []}, "target": {"appUuid": "SWAPP-B64381A0B52D5E91DA7E216EFABF2E9E", "formCode": "PROC-8603F9C9-CB19-4FC4-A750-4106F8CB50CC"}}, "required": true, "spread": false, "behaviorLinkage": []}}, {"componentName": "TextField", "props": {"rely": {"formula": "", "type": "rely", "fields": ["DDSelectField_3YTT0PJ1ZC60"]}, "defaultValue": "", "invisible": false, "label": "项目编码", "placeholder": "请输入", "id": "TextField_1JZ3GWAK3BPC0", "fields": [{"props": {"id": "SeqNumberField_1U20R8HOJISG0"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "DDSelectField_3YTT0PJ1ZC60", "fieldId": "TextField_LWO7JZM7IV40"}]}, "target": {"appUuid": "SWAPP-B64381A0B52D5E91DA7E216EFABF2E9E", "formCode": "PROC-8603F9C9-CB19-4FC4-A750-4106F8CB50CC"}}, "validation": "", "required": true}}, {"componentName": "DDSelectField", "props": {"rely": {"type": "async"}, "options": [], "label": "供应商名称", "placeholder": "请选择", "id": "DDSelectField_J9TYXTJJ7AG0", "fields": [{"suiteId": "", "hidden": false, "children": [], "dataType": "String", "opList": [], "suiteBizType": "", "suiteBizAlias": "", "componentName": "TextField", "displayPosition": "", "formField": true, "props": {"bizType": "", "statField": [], "link": "", "align": "", "durationLabel": "", "required": true, "content": "", "holidayOptions": [], "quote": "", "options": [], "id": "TextField_206YZ2JJXL9C0", "displayGroupBy": "", "placeHolder": "请输入", "behaviorLinkage": [], "objOptions": [], "notUpper": "", "format": "", "bizAlias": "", "label": "供应商名称", "unit": "", "mainTitle": "", "notPrint": "", "fields": [], "attendTypeLabel": "", "actionName": ""}}], "dataSource": {"type": "form", "params": {"filters": []}, "target": {"appUuid": "SWAPP-B64381A0B52D5E91DA7E216EFABF2E9E", "formCode": "PROC-254EF5D3-CB96-4CF5-A02F-B3A5D0143839"}}, "required": true, "spread": false, "behaviorLinkage": []}}, {"componentName": "TextField", "props": {"rely": {"formula": "", "type": "rely", "fields": ["DDSelectField_J9TYXTJJ7AG0"]}, "defaultValue": "", "label": "户名", "placeholder": "请输入", "id": "TextField_3ROF5W270260", "fields": [{"props": {"id": "TextField_6TQ7VNT8GAK0"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "DDSelectField_J9TYXTJJ7AG0", "fieldId": "TextField_206YZ2JJXL9C0"}]}, "target": {"appUuid": "SWAPP-B64381A0B52D5E91DA7E216EFABF2E9E", "formCode": "PROC-254EF5D3-CB96-4CF5-A02F-B3A5D0143839"}}, "validation": "", "required": true}}, {"componentName": "TextField", "props": {"rely": {"formula": "", "type": "rely", "fields": ["DDSelectField_J9TYXTJJ7AG0"]}, "defaultValue": "", "label": "开户行", "placeholder": "请输入", "id": "TextField_1VS7MPWPW29S0", "fields": [{"props": {"id": "TextField_2DJYWXMHGW2S"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "DDSelectField_J9TYXTJJ7AG0", "fieldId": "TextField_206YZ2JJXL9C0"}]}, "target": {"appUuid": "SWAPP-B64381A0B52D5E91DA7E216EFABF2E9E", "formCode": "PROC-254EF5D3-CB96-4CF5-A02F-B3A5D0143839"}}, "validation": "", "required": true}}, {"componentName": "TextField", "props": {"rely": {"formula": "", "type": "rely", "fields": ["DDSelectField_J9TYXTJJ7AG0"]}, "defaultValue": "", "label": "收款账户", "placeholder": "请输入", "id": "TextField_18AZSINZ6CZG0", "fields": [{"props": {"id": "TextField_1B4C2W9AW5340"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "DDSelectField_J9TYXTJJ7AG0", "fieldId": "TextField_206YZ2JJXL9C0"}]}, "target": {"appUuid": "SWAPP-B64381A0B52D5E91DA7E216EFABF2E9E", "formCode": "PROC-254EF5D3-CB96-4CF5-A02F-B3A5D0143839"}}, "validation": "", "required": true}}, {"componentName": "DDSelectField", "props": {"rely": {}, "options": [{"value": "原材料采购", "key": "option_QM8VO2LEB7K0"}, {"value": "成品采购", "key": "option_TIOXAARV39C0"}, {"value": "耗材采购", "key": "option_1HJ55UGFJXGG0"}], "label": "采购类型", "placeholder": "请选择", "id": "DDSelectField_UGIX9Y9NPTS0", "fields": [], "dataSource": {}, "required": true, "spread": false, "behaviorLinkage": []}}, {"children": [{"componentName": "TextField", "props": {"label": "产品名称", "placeholder": "请输入", "id": "TextField_DNMMI88NLCG0", "validation": "", "required": false}}, {"componentName": "NumberField", "props": {"unit": "", "label": "产品数量", "placeholder": "请输入数字", "id": "NumberField_K5W4M9LPJOG", "required": false}}, {"componentName": "NumberField", "props": {"bizAlias": "", "label": "单价（元）", "placeholder": "请输入数字", "id": "NumberField_1JRGNMO0L3CW0", "required": false, "ratio": 50}}, {"componentName": "CalculateField", "props": {"notUpper": "1", "formula": [{"id": "NumberField_K5W4M9LPJOG"}, "*", {"id": "NumberField_1JRGNMO0L3CW0"}], "bizAlias": "", "label": "金额（元）", "placeholder": "自动计算数值", "id": "CalculateField_1GXH4P8TL0CG0"}}], "componentName": "TableField", "props": {"tableViewMode": "table", "rely": {"formula": "", "subFieldsType": "table", "type": "rely", "fields": ["DDSelectField_3YTT0PJ1ZC60"], "subFields": [{"from": "TextField_13M3QKX9HQW00", "to": "TextField_DNMMI88NLCG0"}, {"from": "NumberField_1NH4OV3OMZGG0", "to": "NumberField_K5W4M9LPJOG"}]}, "defaultValue": "", "bizAlias": "", "label": "采购清单", "id": "TableField_1DUG6IV95QG00", "fields": [{"props": {"id": "TableField_INUM5GEV6BC0"}}], "dataSource": {"type": "form", "params": {"filters": [{"valueType": "current", "filterType": "EQ", "value": "DDSelectField_3YTT0PJ1ZC60", "fieldId": "TextField_LWO7JZM7IV40"}]}, "target": {"appUuid": "SWAPP-B64381A0B52D5E91DA7E216EFABF2E9E", "formCode": "PROC-8603F9C9-CB19-4FC4-A750-4106F8CB50CC"}}, "actionName": "添加"}}, {"componentName": "CalculateField", "props": {"notUpper": "0", "formula": [{"id": "CalculateField_1GXH4P8TL0CG0"}], "bizAlias": "", "label": "采购清单合计金额", "placeholder": "自动计算数值", "id": "CalculateField_1A1MWVLL5AG00"}}, {"componentName": "DDSelectField", "props": {"rely": {}, "options": [{"value": "对公付款", "key": "option_0"}, {"value": "对私付款", "key": "option_1"}], "label": "付款类型", "placeholder": "请选择", "id": "DDSelectField_10MOREY42Z4G0", "fields": [], "dataSource": {}, "required": true, "spread": false, "behaviorLinkage": []}}, {"componentName": "MoneyField", "props": {"notUpper": "0", "label": "付款金额（元）", "placeholder": "请输入金额", "id": "MoneyField_9P26SV7H9SG0", "required": true}}, {"componentName": "TextareaField", "props": {"label": "备注", "placeholder": "请输入", "id": "TextareaField_1AGQFN7PHZJ40", "required": false}}, {"componentName": "DDAttachment", "props": {"label": "附件", "id": "DDAttachment_1J3POYMHDEIO0", "required": false}}, {"componentName": "DDPhotoField", "props": {"watermark": false, "label": "图片", "id": "DDPhotoField_F2ML25H6VDK0", "useAlbum": true, "required": false}}]