import 'babel-polyfill';
import './init';
import React from 'react';
import ReactDOM from 'react-dom';
import get from 'lodash/get';

import { ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';

import RenderEngine from '../src/engine';
import trunkProtoPC from '../src/trunk.web.proto'; // 设计器
import trunkProto from '../src/trunk.proto'; // 设计器H5
import trunkMobile from '../src/trunk.mobile'; // 内部的移动端
import trunkPC from '../src/trunk.pc'; // 内部的pc端
import trunkWeb from '../src/trunk.web'; // 外部的pc
import trunkH5 from '../src/trunk.h5'; // 外部的h5
import './preview.less';
import fetch from 'isomorphic-fetch';

import parseJSON from '@ali/sw-utils/lib/string/parseJSON';



const corpid = location.search.match(/corpid=([^&]*)/);
const viewMode = /viewMode/.test(location.href);
const disabled = /disabled/.test(location.href);

window.__corpid = corpid ? corpid[1] : 'ding4e03c0afa42b32c835c2f4657eb6378f';
window.__WPO = {
  log() { },
  custom() { },
  retCode() { },
};
window.__user__ = {
  workNo: '33223334',
};
window.__isMock = true;

window.trialFeatures = {
  trials: {
    enableFlowDesign: true,
    tableFieldUiUpgrade_common: true,
    show_mask_form_inst_data: true,
    enableAutoWrapInTextField: true,
    enableLeaveRulePriority: true,
    checkLandlineNumber_service: true,
    enable_lwp_cache_in_memory: true,
  },
}

const getUrlParam = name => {
  const reg = new RegExp(`${name}=([^&]*)(&|$)`, 'i');
  const r = window.location.search.match(reg);
  if (r != null) {
    return unescape(r[1]);
  }
  return null;
};
const mode = getUrlParam('mode');
const action = getUrlParam('action') || 'edit';
const enableLocalData = getUrlParam('enableLocalData');

class Preview extends React.Component {
  state = {};
  componentDidMount() {
    const trunkMap = {
      pc: trunkPC,
      mobile: trunkMobile,
      h5: trunkH5,
      web: trunkWeb,
      proto: trunkProto,
      protoPC: trunkProtoPC,
    };

    const components = trunkMap[mode] || [];
    window.__swappBenefits = 'DINGFLOW_ADVANCED_EDITION,DINGFLOW_FORM_SELECTION_LINKAGE,B_OAPLUS_PREMIUM';
    let ctx;

    // fetch('/mock/approval/dataMask.json', {})
    fetch('/mock/form/allComponents.json', {})
    // fetch('/mock/form/survey.json', {})
    // fetch('/mock/formSchema_relate.json', {})
    // fetch('/mock/formSchema_rely.json', {})
    // fetch('/mock/rely/lingyi.json', {})
    // fetch('/mock/suite/test.json', {})
    // fetch('/mock/linkage/cascade.json', {})
    // fetch('/mock/palm/plan.json', {})
    // fetch('/mock/approval/allExcel.json', {})
    // fetch('/mock/approval/AI.json', {})
    // fetch('/mock/approval/formula.json', {})
      // fetch('/mock/approval/openDataField.json')
      // fetch('/mock/approval/file.json', {})
      // fetch('/mock/linkage/option_delete.json', {})
      .then(res => res.json())
      .then(formSchema => {
        let formData = [];
        let ctxProps = {};
        if (action === 'edit' || viewMode || disabled) {
          // fetch('/mock/data/dataMask.json', {})
          fetch('/mock/data/allComponents.json', {})
          // fetch('/mock/data/test1.json', {})
          // fetch('/mock/data/file.json', {})
          // fetch('/mock/data/AI.json', {})
            // fetch('/mock/data/file.json', {})
            // fetch('/mock/data/openDataField.json')
            .then(res => res.json())
            .then(data => {
              console.log('formSchema1', formSchema, data, viewMode);

              // console.log(Object.keys(fdata).map(v => ({ key: v, ...fdata[v]})), "-----------")
              ctxProps = ({
                viewMode,
                disabled,
                hideEmptyInViewMode:
                  getUrlParam('hideEmptyInViewMode') !== 'false',
                showLabelIndex: getUrlParam('showLabelIndex'),
                layout: getUrlParam('layout') || 'horizontal',
                components,
                debugMode: true,
                params: {
                  scene: viewMode ? 'detail' : 'create',
                  enableTextCorrect: false,
                  enableImportFromClipboard: true,
                  processCode: 'PROC-D3B49DB2-E4C6-43ED-A933-FA72B1FBB601',
                  procInstId: 'test-proc-inst-id',
                  corpId: window.__corpid || 'testCorpid',
                  uploadImageUrl:
                    'https://pre-swform.dingtalk.com/lwpForm/uploadFile?formCode=PROC-BC57C45A-A42F-48C6-8EC1-88C79B44547F&corpId=dingc2b9439eb5f04acd35c2f4657eb6378f',
                },
                schema: formSchema,
                data,
                fieldSettings: [
                  {
                    componentName: 'TableField',
                    fieldBehavior: 'READONLY',
                    fieldId: 'TableField_1NENDTT9QMU80',
                  },
                ],
                // maskConfig: {
                //   fetchData: () => {
                //     return Promise.resolve({
                //       value: '11',
                //     });
                //   }
                // },
                // hooks: [
                //   {
                //     trigger: {
                //       fields: ['InnerContactField_1INCS3747D280'],
                //       type: 'onValueChange',
                //     },
                //     dataSource: {
                //       type: 'connector',
                //       target: {
                //         actionInstanceId: 'G-ACT-INST-1012881DA032212C12800007',
                //       },
                //     },
                //   },
                //   {
                //     trigger: {
                //       fields: ['InnerContactField_F7SHGP25ZVS0'],
                //       type: 'onValueChange',
                //     },
                //     dataSource: {
                //       type: 'connector',
                //       target: {
                //         actionInstanceId: 'G-ACT-INST-10128829730C0B8AD0D90008',
                //       },
                //     },
                //   },
                //   {
                //     trigger: {
                //       fields: ['DDSelectField_1H16QV5VCRPC0'],
                //       type: 'onValueChange',
                //     },
                //     dataSource: {
                //       type: 'connector',
                //       target: {
                //         actionInstanceId: 'G-ACT-INST-10134C2426250B15509F000Q',
                //       },
                //     },
                //   },
                //   {
                //     trigger: {
                //       fields: [
                //         'TableField_8HFL88PIINK0.DDSelectField_12GD6ED3H1400',
                //       ],
                //       type: 'onValueChange',
                //     },
                //     dataSource: {
                //       type: 'connector',
                //         actionInstanceId: 'G-ACT-INST-10134C330F590B15509F000M',
                //       },
                //     },
                //   },
                //   {
                //     trigger: {
                //       fields: ['DDSelectField_1H16QV5VCRPC0'],
                //       type: 'init',
                //     },
                //     dataSource: {
                //       type: 'connector',
                //       target: {
                //         actionInstanceId: 'G-ACT-INST-10129BA59B010B8D133A0008',
                //       },
                //     },
                //     dataSource: {
                //       type: 'connector',
                //       target: {
                //         actionInstanceId: 'G-ACT-INST-10134C14E3150B15509F000M',
                //       },
                //     },
                //   },
                // ],
                data: enableLocalData
                  ? parseJSON(localStorage.getItem('formData'), data)
                  : data,
              });

              // ctx.getFieldById('TableField_1DUG6IV95QG00').setFieldIntercept('intercept_table_field_before_delete_row', ({index}) =>  {
              //   console.log('jamesxu',index);
              //   return new Promise((resovle, reject) => {
              //     reject();
              //   })
              // })
              // ctx.onFormDataChange(() => {
              //   console.log(ctx.getFormData());
              // });
              // ctx = RenderEngine.createPage(ctxProps);
              this.setState({
                // ctx,
                ctxProps,
                formRenderKey: 'first',
              });
              setTimeout(() => {
                this.forceUpdate();
              }, 3000);
            });
        } else {
          ctxProps = ({
            viewMode,
            disabled,
            layout: getUrlParam('layout') || 'horizontal',
            params: {
              corpId: window.__corpid || 'dingb1f7bb907a7b302135c2f4657eb6378f',
              // uploadImageUrl: 'https://pre-swform.dingtalk.com/lwpForm/uploadFile?formCode=PROC-BC57C45A-A42F-48C6-8EC1-88C79B44547F&corpId=dingc2b9439eb5f04acd35c2f4657eb6378f',
            },
            schema: formSchema,
            hooks: [
              {
                trigger: {
                  fields: ['TextField-K2AD4O5B'],
                  type: 'onValueChange',
                },
                dataSource: {
                  type: 'connector',
                  target: {
                    actionInstanceId: 'G-ACT-INST-10122BFBBF870B15509F000B',
                  },
                },
              },
            ],
            fieldSettings: [
              {
                componentName: 'DDDateField',
                fieldBehavior: 'READONLY',
                fieldId: 'DDDateField_JCGEAVMM03K0',
              },
            ],
            data: [
              {
                extendValue: null,
                key: 'DDDateField_JCGEAVMM03K0',
                value: '2010-12-12',
              },
            ],
            components,
            debugMode: true,
          });
          this.setState({
            // ctx,
            ctxProps,
          });
        }
      });

    enableLocalData && setInterval(() => {
      this.saveLocalData();
    }, 2000);
  }

  dynamicClick = async () => {
    const ctx = this.state.ctx;
    const dynamicFieldId = 'DynamicField-newsalary';
    const dynamicFieldStore = ctx.getFieldById(dynamicFieldId);
    dynamicFieldStore.setChildren([
      {
        componentName: 'IdCardField',
        props: {
          label: '身份证号22',
          placeholder: '请输入身份证',
          required: true,
          id: 'IdCardField-testaaa',
        },
        value: '1111',
      },
    ]);
  };

  submit = async () => {
    const ctx = this.state.ctx;
    const pendingMap = ctx.pendingMap || {};
    console.log('pendingMap', pendingMap);
    if (Object.keys(pendingMap).length > 0) {
      alert('公式计算中，请稍后提交...');
      return;
    }
    const value = ctx.getFormData();

    console.log('value', value);


    const { valid, message } = await ctx.validateFormData();
    console.log({ valid, message });
    if (valid) {
      const value = ctx.getFormData();
      alert(JSON.stringify(value));
    } else {
      ctx.scrollToInvalidField();
    }
  };

  saveLocalData = async () => {
    const ctx = this.state.ctx;
    if (!ctx) {
      return;
    }
    const value = await ctx.getFormData();
    console.log('saveData', value);
    localStorage.setItem('formData', JSON.stringify(value));
  }

  render() {
    const ctx = this.state.ctx;
    window.ctx = ctx;
    return (
      <div>
        {!viewMode && mode === 'pc' && <div className='oa-custom-page-header'></div>}
        <div className={`main ${mode}`}>
          {/*<button className="button" onClick={this.dynamicClick}>Dynamic get</button>*/}
          {/* {ctx && ctx.render()} */}
          {this.state.ctxProps && <RenderEngine.Page key={this.state.formRenderKey} {...this.state.ctxProps} onInit={(ctx) => this.setState({ ctx })} />}
          <button className="button" onClick={this.submit}>
            提交
          </button>
        </div>
      </div>
    );
  }
}

ReactDOM.render(
  <ConfigProvider locale={zhCN}>
    <Preview />
  </ConfigProvider>,
  document.getElementById('App'),
);
