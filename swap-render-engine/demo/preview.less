/* reset start
  fixme 此处样式应在 dd-design中实现
*/

// 需要补充class设置label的line-height
.dtm-list-item .dtm-list-line .dtm-list-content {
  line-height: 24px;
  white-space: normal;
  overflow: hidden;
}
// 需要加前缀仅用于FormItem
.dtm-list-item .dtm-list-brief {
  color: var(--common_level1_base_color, rgba(23, 26, 29, 1)); // 默认颜色
}

// 需要加前缀仅用于FormItem
.dtm-list-item.dtm-list-item-disabled .dtm-list-line .dtm-list-content .dtm-list-brief {
  color: var(--common_level4_base_color, rgba(23, 26, 29, 0.24)); // 禁用态
}

.dtm-textarea-item-item {
  position: relative; // 用于计数element显示的定位
}

/* reset end */


.m-form-viewMode {
  background: #fff;
}

#App {
  overflow: hidden;
  min-height: 100vh;
}
.m-form {
  min-height: 500px;
}

.button {
  outline: none;
  width: 100%;
  font-size: 16px;
  padding: 16px 0;
  color: #fff;
  background: #3296fa;
  display: block;
  border: none;
}
.oa-custom-page-header {
  height: 54px;
  position: fixed;
  top: 0;
  width: 100%;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1px solid rgba(17, 31, 44, 0.08);
}
.main {
  position: relative;
  font-size: 17px;
  background: #f8f8f8;
  height: 100%;
  overflow: auto;
  background-color: #f5f5f9;
  &.web,
  &.pc {
    font-size: 14px;
    margin: 0px auto;
    padding: 20px;
    padding-top: 54px;
    border: 1px solid #d8d8d8;
    background: #fff;

    &>button {
      padding: 10px 20px;
      margin: 0 auto;
    }
  }
}

.main input,
textarea {
  outline: none;
}

.main input:focus,
textarea:focus {
  border-color: #aaddfe;
}

.main .subs-row {
  // padding-top: 10px;
}

/* 从 ding-flow-pc/src/pages/CustomPage/customSubmit.styl copy过来 */
/*审批表单外框*/
.wf-main {
  padding: 25px 20px;
  color: #333;
  font-size: 13px;
}

.wf-main .subs-row {
  padding-top: 10px;
}

/*提交按钮*/
.submit-foot {
  width: 200px;
  margin-top: 20px;
  margin-bottom: 20px;
  height: 36px;
  line-height: 36px;
  font-size: 16px;
  color: #fff;
  text-align: center;
  border-radius: 36px;
  background-color: #0088f1;
  cursor: pointer;
}

.submit-foot:active {
  opacity: 0.6;
}

*::-webkit-input-placeholder {
  color: rgba(23,26,29,0.24);
}

.ddEdit.only:before {
  content: '';
}

.ddEdit {
  width: 80%;
  max-width: 920px;
}

.moneyfield.cnformat {
  margin-top: -8px;
  color: #999;
}

.moneyfield.cnformat .ddLabel,
.moneyfield.cnformat .ddEdit {
  padding-top: 0px;
}

.statline {
  border-top: 1px solid #ededed;
}

.statline-text {
  width: 20%;
  text-align: right;
  display: inline-block;
}

.ddMainLabel {
  display: block;
  width: 80%;
  max-width: 700px;
  color: #858e99;
  font-size: 12px;
  margin: 16px 0;
}


input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  display: none;
}

.disable {
  color: #999999 !important;
  cursor: default !important;
}


.webPluginReq {
  color: #ea6d5c;
}
.ddDesc {
  color: #bfc0c1;
}

.notSupportArea {
  padding: 20px 0;
  font-weight: bold;
}
